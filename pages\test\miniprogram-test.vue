<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">小程序测试页面</text>
      <text class="test-subtitle">用于诊断小程序页面空白问题</text>
    </view>
    
    <view class="test-content">
      <view class="test-section">
        <text class="section-title">系统信息</text>
        <view class="info-item">
          <text class="info-label">平台:</text>
          <text class="info-value">{{ systemInfo.platform }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">版本:</text>
          <text class="info-value">{{ systemInfo.version }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">状态栏高度:</text>
          <text class="info-value">{{ systemInfo.statusBarHeight }}px</text>
        </view>
      </view>
      
      <view class="test-section">
        <text class="section-title">登录状态</text>
        <view class="info-item">
          <text class="info-label">Token:</text>
          <text class="info-value">{{ loginStatus.token ? '已设置' : '未设置' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">UID:</text>
          <text class="info-value">{{ loginStatus.uid || '未设置' }}</text>
        </view>
      </view>
      
      <view class="test-section">
        <text class="section-title">网络测试</text>
        <button class="test-button" @click="testNetwork">测试网络连接</button>
        <text class="test-result">{{ networkResult }}</text>
      </view>
      
      <view class="test-section">
        <text class="section-title">WebSocket测试</text>
        <button class="test-button" @click="testWebSocket">测试WebSocket连接</button>
        <text class="test-result">{{ wsResult }}</text>
      </view>
      
      <view class="test-section">
        <text class="section-title">页面跳转测试</text>
        <button class="test-button" @click="goToHome">跳转到首页</button>
        <button class="test-button" @click="goToLogin">跳转到登录页</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      systemInfo: {},
      loginStatus: {
        token: '',
        uid: ''
      },
      networkResult: '未测试',
      wsResult: '未测试'
    }
  },
  
  onLoad() {
    console.log('🧪 小程序测试页面加载')
    this.initSystemInfo()
    this.checkLoginStatus()
  },
  
  methods: {
    initSystemInfo() {
      try {
        this.systemInfo = uni.getSystemInfoSync()
        console.log('系统信息:', this.systemInfo)
      } catch (error) {
        console.error('获取系统信息失败:', error)
        this.systemInfo = { platform: '获取失败', version: '未知', statusBarHeight: 0 }
      }
    },
    
    checkLoginStatus() {
      try {
        this.loginStatus.token = uni.getStorageSync('user_token')
        this.loginStatus.uid = uni.getStorageSync('user_uid')
        console.log('登录状态:', this.loginStatus)
      } catch (error) {
        console.error('检查登录状态失败:', error)
      }
    },
    
    async testNetwork() {
      this.networkResult = '测试中...'
      try {
        const result = await uni.request({
          url: 'https://ceshi.huisas.com/api.php?act=query&pid=99009&key=123456',
          method: 'GET',
          timeout: 10000
        })
        
        if (result.statusCode === 200) {
          this.networkResult = '网络连接正常'
          console.log('网络测试成功:', result.data)
        } else {
          this.networkResult = `网络错误: ${result.statusCode}`
        }
      } catch (error) {
        this.networkResult = `网络测试失败: ${error.message}`
        console.error('网络测试失败:', error)
      }
    },
    
    testWebSocket() {
      this.wsResult = '测试中...'
      try {
        const socketTask = uni.connectSocket({
          url: 'wss://ceshi.huisas.com:8080',
          success: () => {
            console.log('WebSocket连接成功')
            this.wsResult = 'WebSocket连接成功'
            socketTask.close()
          },
          fail: (error) => {
            console.error('WebSocket连接失败:', error)
            this.wsResult = `WebSocket连接失败: ${error.errMsg}`
          }
        })
      } catch (error) {
        this.wsResult = `WebSocket测试失败: ${error.message}`
        console.error('WebSocket测试失败:', error)
      }
    },
    
    goToHome() {
      uni.switchTab({
        url: '/pages/index/index',
        success: () => {
          console.log('跳转首页成功')
        },
        fail: (error) => {
          console.error('跳转首页失败:', error)
          uni.showToast({
            title: '跳转失败',
            icon: 'none'
          })
        }
      })
    },
    
    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/index',
        success: () => {
          console.log('跳转登录页成功')
        },
        fail: (error) => {
          console.error('跳转登录页失败:', error)
          uni.showToast({
            title: '跳转失败',
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.test-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .test-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }
  
  .test-subtitle {
    display: block;
    font-size: 28rpx;
    color: #666;
  }
}

.test-content {
  .test-section {
    background: white;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    
    .section-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .info-label {
        font-size: 28rpx;
        color: #666;
      }
      
      .info-value {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }
    
    .test-button {
      background: #5145F7;
      color: white;
      border: none;
      border-radius: 12rpx;
      padding: 20rpx 40rpx;
      font-size: 28rpx;
      margin: 10rpx 10rpx 10rpx 0;
    }
    
    .test-result {
      display: block;
      font-size: 26rpx;
      color: #666;
      margin-top: 20rpx;
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 8rpx;
    }
  }
}
</style>
