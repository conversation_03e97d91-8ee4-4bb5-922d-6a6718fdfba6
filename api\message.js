// api/message.js - 消息相关API
import { request } from '@/utils/request'

/**
 * 获取公告消息列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，默认1
 * @param {number} params.limit - 每页数量，默认20
 * @param {string} params.type - 消息类型：all(全部)、system(系统)、transaction(交易)、activity(活动)
 * @returns {Promise} 消息列表
 */
export function getMessageList(params = {}) {
  return request({
    url: '/user/ajax.php',
    method: 'POST',
    data: {
      act: 'getMessageList',
      page: params.page || 1,
      limit: params.limit || 20,
      type: params.type || 'all'
    }
  })
}

/**
 * 获取公告通知列表（兼容原有接口）
 * @returns {Promise} 公告列表
 */
export function getAnnouncementList() {
  return request({
    url: '/user/ajax.php',
    method: 'POST',
    data: {
      act: 'getAnnouncementList'
    }
  })
}

/**
 * 获取消息详情
 * @param {number} messageId - 消息ID
 * @returns {Promise} 消息详情
 */
export function getMessageDetail(messageId) {
  return request({
    url: '/user/ajax.php',
    method: 'POST',
    data: {
      act: 'getMessageDetail',
      message_id: messageId
    }
  })
}

/**
 * 标记消息为已读
 * @param {number} messageId - 消息ID
 * @returns {Promise} 操作结果
 */
export function markMessageAsRead(messageId) {
  return request({
    url: '/user/ajax.php',
    method: 'POST',
    data: {
      act: 'markMessageAsRead',
      message_id: messageId
    }
  })
}

/**
 * 批量标记消息为已读
 * @param {Array} messageIds - 消息ID数组
 * @returns {Promise} 操作结果
 */
export function markMessagesAsRead(messageIds) {
  return request({
    url: '/user/ajax.php',
    method: 'POST',
    data: {
      act: 'markMessagesAsRead',
      message_ids: messageIds.join(',')
    }
  })
}

/**
 * 删除消息
 * @param {number} messageId - 消息ID
 * @returns {Promise} 操作结果
 */
export function deleteMessage(messageId) {
  return request({
    url: '/user/ajax.php',
    method: 'POST',
    data: {
      act: 'deleteMessage',
      message_id: messageId
    }
  })
}

/**
 * 清空所有消息
 * @param {string} type - 消息类型，可选
 * @returns {Promise} 操作结果
 */
export function clearAllMessages(type = 'all') {
  return request({
    url: '/user/ajax.php',
    method: 'POST',
    data: {
      act: 'clearAllMessages',
      type: type
    }
  })
}

/**
 * 获取未读消息数量
 * @returns {Promise} 未读消息数量
 */
export function getUnreadMessageCount() {
  return request({
    url: '/user/ajax.php',
    method: 'POST',
    data: {
      act: 'getUnreadMessageCount'
    }
  })
}

export default {
  getMessageList,
  getAnnouncementList,
  getMessageDetail,
  markMessageAsRead,
  markMessagesAsRead,
  deleteMessage,
  clearAllMessages,
  getUnreadMessageCount
}
