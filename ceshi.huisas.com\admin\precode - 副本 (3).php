<?php
/**
 * 预制收款码管理
**/
include("../includes/common.php");

// 处理批量生成 - 必须在任何HTML输出之前
if(isset($_POST['num'])){
    // 清除任何之前的输出
    if (ob_get_level()) {
        ob_end_clean();
    }
    header('Content-Type: application/json; charset=utf-8');

    $num = intval($_POST['num']);
    if($num <= 0 || $num > 100){
        exit(json_encode(['code'=>-1, 'msg'=>'生成数量必须在1-100之间'], JSON_UNESCAPED_UNICODE));
    }

    // 检查函数是否存在
    if(!function_exists('generatePreCode')){
        exit(json_encode(['code'=>-1, 'msg'=>'generatePreCode函数不存在'], JSON_UNESCAPED_UNICODE));
    }

    $success = 0;
    $codes = array();
    try {
        $DB->beginTransaction();
        for($i=0; $i<$num; $i++){
            $code = generatePreCode();
            if(!$code) {
                error_log("生成预制码失败，第{$i}次");
                continue;
            }
            $qr_url = $siteurl . 'paypage/precode.php?code=' . $code;
            $sql = $DB->exec("INSERT INTO `{$dbconfig['dbqz']}_precode` (`code`, `qr_url`, `addtime`) VALUES (:code, :qr_url, NOW())", [':code'=>$code, ':qr_url'=>$qr_url]);
            if($sql !== false){
                $success++;
                $codes[] = $code;
            } else {
                error_log("插入数据库失败: " . $code);
            }
        }
        $DB->commit();
        $codes_str = implode("<br>", $codes);
        exit(json_encode(['code'=>0, 'msg'=>"成功生成 {$success} 个预制码<br>{$codes_str}"], JSON_UNESCAPED_UNICODE));
    } catch (Exception $e) {
        $DB->rollBack();
        error_log("批量生成异常: " . $e->getMessage());
        exit(json_encode(['code'=>-1, 'msg'=>'生成失败：'.$e->getMessage()], JSON_UNESCAPED_UNICODE));
    }
}

$title='预制收款码管理';
include './head.php';
if($islogin==1){}else exit("<script language='javascript'>window.location.href='./login.php';</script>");

// 设置错误报告
error_reporting(E_ALL & ~E_NOTICE);
ini_set('display_errors', 0); // 生产环境关闭错误显示

// 检查并创建表
$DB->exec("CREATE TABLE IF NOT EXISTS `{$dbconfig['dbqz']}_precode` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `code` varchar(50) NOT NULL COMMENT '预制码',
    `qr_url` text COMMENT '完整的二维码URL',
    `uid` int(11) DEFAULT NULL COMMENT '绑定的商户ID',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0未绑定 1已绑定',
    `addtime` datetime DEFAULT NULL COMMENT '生成时间',
    `bindtime` datetime DEFAULT NULL COMMENT '绑定时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预制收款码';");

// 处理删除
if(isset($_GET['action']) && $_GET['action']=='delete' && isset($_GET['id'])){
    $id = intval($_GET['id']);
    $row = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_precode` WHERE `id`=:id LIMIT 1", [':id'=>$id]);
    if(!$row){
        $msg = '预制码不存在';
    } elseif($row['status'] == 1){
        $msg = '已绑定的预制码不能删除';
    } else {
        $sql = $DB->exec("DELETE FROM `{$dbconfig['dbqz']}_precode` WHERE `id`=:id", [':id'=>$id]);
        if($sql !== false){
            $msg = '删除成功';
        } else {
            $msg = '删除失败';
        }
    }
}

// 处理解绑
if(isset($_GET['action']) && $_GET['action']=='unbind' && isset($_GET['id'])){
    $id = intval($_GET['id']);
    $row = $DB->getRow("SELECT * FROM `{$dbconfig['dbqz']}_precode` WHERE `id`=:id LIMIT 1", [':id'=>$id]);
    if(!$row){
        $msg = '预制码不存在';
    } elseif($row['status'] == 0){
        $msg = '该预制码未绑定';
    } else {
        $sql = $DB->exec("UPDATE `{$dbconfig['dbqz']}_precode` SET `uid`=NULL, `status`=0, `bindtime`=NULL WHERE `id`=:id", [':id'=>$id]);
        if($sql !== false){
            $msg = '解绑成功';
        } else {
            $msg = '解绑失败';
        }
    }
}

// 查询条件
$where = " 1=1";
$params = [];
if(isset($_GET['code']) && !empty($_GET['code'])) {
    $code = trim($_GET['code']);
    $where .= " AND `code` LIKE :code";
    $params[':code'] = '%'.$code.'%';
}
if(isset($_GET['uid']) && !empty($_GET['uid'])) {
    $uid = intval($_GET['uid']);
    $where .= " AND `uid`=:uid";
    $params[':uid'] = $uid;
}
if(isset($_GET['status']) && $_GET['status']!=='') {
    $status = intval($_GET['status']);
    $where .= " AND `status`=:status";
    $params[':status'] = $status;
}

// 分页
$numrows = $DB->getColumn("SELECT COUNT(*) from `{$dbconfig['dbqz']}_precode` WHERE{$where}", $params);
$pagesize = 30;
$pages = ceil($numrows / $pagesize);
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$offset = ($page - 1) * $pagesize;

$list = $DB->getAll("SELECT a.*,b.username FROM `{$dbconfig['dbqz']}_precode` a LEFT JOIN `{$dbconfig['dbqz']}_user` b ON a.uid=b.uid WHERE{$where} ORDER BY a.id DESC LIMIT $offset,$pagesize", $params);
?>

<div class="container" style="padding-top:70px;">
<div class="col-md-12 center-block" style="float: none;">
<div class="panel panel-primary">
<div class="panel-heading"><h3 class="panel-title">预制收款码管理</h3></div>
<div class="panel-body">
<?php if(isset($msg)){
    echo '<div class="alert alert-info">'.$msg.'</div>';
}?>
<form action="" method="GET" class="form-inline">
  <div class="form-group">
    <label>收款码：</label>
    <input type="text" class="form-control" name="code" value="<?php echo isset($_GET['code'])?htmlspecialchars($_GET['code']):''?>">
  </div>
  <div class="form-group">
    <label>商户ID：</label>
    <input type="text" class="form-control" name="uid" value="<?php echo isset($_GET['uid'])?htmlspecialchars($_GET['uid']):''?>">
  </div>
  <div class="form-group">
    <label>状态：</label>
    <select name="status" class="form-control">
      <option value="">全部状态</option>
      <option value="0" <?php echo isset($_GET['status']) && $_GET['status']==='0'?'selected':''?>>未绑定</option>
      <option value="1" <?php echo isset($_GET['status']) && $_GET['status']==='1'?'selected':''?>>已绑定</option>
    </select>
  </div>
  <div class="form-group">
    <button type="submit" class="btn btn-primary">搜索</button>
    <a href="precode.php" class="btn btn-default">重置</a>
  </div>
</form>

<p style="margin-top: 10px;">
  <a href="javascript:void(0)" class="btn btn-success" onclick="batchGenerate()">批量生成</a>
  <a href="javascript:void(0)" class="btn btn-info" onclick="batchDownloadQR()">批量下载二维码</a>
  <span class="pull-right">共有 <?php echo $numrows?> 条记录</span>
</p>

<div class="table-responsive">
<table class="table table-striped table-bordered table-hover">
<thead>
  <tr>
    <th>ID</th>
    <th>预制收款码</th>
    <th>绑定商户</th>
    <th>生成时间</th>
    <th>状态</th>
    <th>操作</th>
  </tr>
</thead>
<tbody>
<?php
if($list) {
    foreach($list as $row) {
        echo '<tr>';
        echo '<td>'.$row['id'].'</td>';
        echo '<td>'.$row['code'].'</td>';
        echo '<td>'.($row['username']?$row['username']:'').'</td>';
        echo '<td>'.$row['addtime'].'</td>';
        echo '<td>'.($row['status']==1?'<span class="label label-success">已绑定</span>':'<span class="label label-default">未绑定</span>').'</td>';
        echo '<td>';
        echo '<button class="btn btn-xs btn-info" onclick="copyToClipboard(\''.$row['code'].'\')">复制</button>';
        echo ' <button class="btn btn-xs btn-primary" onclick="viewQR(\''.$row['code'].'\')">查看二维码</button>';
        echo ' <button class="btn btn-xs btn-success" onclick="downloadQR(\''.$row['code'].'\')">下载二维码</button>';
        if($row['status'] == 0) {
            echo ' <a href="precode.php?action=delete&id='.$row['id'].'" class="btn btn-xs btn-danger" onclick="return confirm(\'确定要删除此预制码吗？\')">删除</a>';
        } else {
            echo ' <a href="precode.php?action=unbind&id='.$row['id'].'" class="btn btn-xs btn-warning" onclick="return confirm(\'确定要解绑此预制码吗？\')">解绑</a>';
        }
        echo '</td>';
        echo '</tr>';
    }
} else {
    echo '<tr><td colspan="6" class="text-center">没有记录</td></tr>';
}
?>
</tbody>
</table>
</div>

<?php
echo '<ul class="pagination">';
$first=1;
$prev=$page-1;
$next=$page+1;
$last=$pages;
if ($page>1)
{
echo '<li><a href="precode.php?page='.$first.$link.'">首页</a></li>';
echo '<li><a href="precode.php?page='.$prev.$link.'">&laquo;</a></li>';
} else {
echo '<li class="disabled"><a>首页</a></li>';
echo '<li class="disabled"><a>&laquo;</a></li>';
}
$start=$page-10>1?$page-10:1;
$end=$page+10<$pages?$page+10:$pages;
for ($i=$start;$i<=$end;$i++)
{
if ($i==$page)
echo '<li class="active"><a>'.$i.'</a></li>';
else
echo '<li><a href="precode.php?page='.$i.$link.'">'.$i.'</a></li>';
}
if ($page<$pages)
{
echo '<li><a href="precode.php?page='.$next.$link.'">&raquo;</a></li>';
echo '<li><a href="precode.php?page='.$last.$link.'">尾页</a></li>';
} else {
echo '<li class="disabled"><a>&raquo;</a></li>';
echo '<li class="disabled"><a>尾页</a></li>';
}
echo '</ul>';
?>

</div>
</div>
</div>
</div>

<script src="<?php echo $cdnpublic?>layer/3.1.1/layer.min.js"></script>
<script>
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        // 使用现代API
        navigator.clipboard.writeText(text).then(function() {
            layer.msg('复制成功！', {icon: 1});
        }).catch(function() {
            fallbackCopy(text);
        });
    } else {
        fallbackCopy(text);
    }
}

function fallbackCopy(text) {
    var temp = document.createElement('input');
    temp.setAttribute('value', text);
    document.body.appendChild(temp);
    temp.select();
    try {
        document.execCommand('copy');
        layer.msg('复制成功！', {icon: 1});
    } catch(err) {
        layer.msg('复制失败，请手动复制', {icon: 2});
    }
    document.body.removeChild(temp);
}

function batchGenerate() {
    layer.prompt({title: '请输入要生成的数量（最多100个）', formType: 0}, function(num, index){
        layer.close(index);

        // 验证输入
        if(!num || isNaN(num) || num < 1 || num > 100) {
            layer.alert('请输入1-100之间的数字', {icon: 2});
            return;
        }

        var ii = layer.load(2, {shade:[0.1,'#fff']});

        // 使用fetch API替代jQuery
        fetch('precode.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'num=' + encodeURIComponent(num)
        })
        .then(response => {
            // 检查响应状态
            if (!response.ok) {
                throw new Error('HTTP ' + response.status + ': ' + response.statusText);
            }
            return response.text(); // 先获取文本
        })
        .then(text => {
            layer.close(ii);
            console.log('服务器响应:', text); // 调试输出

            try {
                var data = JSON.parse(text);
                if(data.code == 0){
                    layer.alert(data.msg, {
                        title: '生成结果',
                        area: ['400px', '300px'],
                        icon: 1
                    }, function(index){
                        layer.close(index);
                        location.reload();
                    });
                }else{
                    layer.alert(data.msg, {icon:2});
                }
            } catch(e) {
                layer.alert('服务器返回格式错误：' + text.substring(0, 200), {icon: 2});
                console.error('JSON解析失败:', e, '原始响应:', text);
            }
        })
        .catch(error => {
            layer.close(ii);
            layer.alert('网络错误：' + error.message, {icon: 2});
            console.error('请求失败:', error);
        });
    });
}

function viewQR(code) {
    try {
        var url = '<?php echo $siteurl; ?>paypage/precode.php?code=' + code;
        var qrUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' + encodeURIComponent(url);

        var content = '<div style="text-align:center; padding:20px;">' +
                     '<h4>预制码: ' + code + '</h4>' +
                     '<img src="' + qrUrl + '" style="max-width:100%; margin:10px 0;" />' +
                     '<p style="color:#666; font-size:12px; margin-top:10px;">扫描二维码进行绑定或支付</p>' +
                     '<p style="color:#999; font-size:11px; word-break:break-all;">URL: ' + url + '</p>' +
                     '</div>';

        layer.open({
            type: 1,
            title: '二维码预览',
            area: ['400px', '500px'],
            content: content,
            btn: ['下载二维码', '关闭'],
            yes: function(index, layero) {
                downloadQR(code);
            }
        });
    } catch(error) {
        layer.msg('显示失败：' + error.message, {icon: 2});
        console.error('显示错误:', error);
    }
}

function downloadQR(code) {
    try {
        var url = '<?php echo $siteurl; ?>paypage/precode.php?code=' + code;
        var qrUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' + encodeURIComponent(url);

        // 使用fetch下载图片并创建blob
        fetch(qrUrl)
            .then(response => response.blob())
            .then(blob => {
                var link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'precode_' + code + '.png';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(link.href);
                layer.msg('二维码下载成功', {icon: 1});
            })
            .catch(error => {
                console.error('下载失败:', error);
                // 如果fetch失败，尝试直接打开链接
                window.open(qrUrl, '_blank');
                layer.msg('请右键保存图片', {icon: 2});
            });
    } catch(error) {
        layer.msg('下载失败：' + error.message, {icon: 2});
        console.error('下载错误:', error);
    }
}

function batchDownloadQR() {
    // 获取当前页面所有的预制码
    var codes = [];
    var rows = document.querySelectorAll('tbody tr');

    rows.forEach(function(row) {
        var cells = row.querySelectorAll('td');
        if(cells.length >= 2) {
            var code = cells[1].textContent.trim();
            if(code && code !== '没有记录') {
                codes.push(code);
            }
        }
    });

    if(codes.length === 0) {
        layer.msg('没有可下载的二维码', {icon: 2});
        return;
    }

    layer.confirm('确定要下载当前页面的 ' + codes.length + ' 个二维码吗？<br><small style="color:#999;">注意：请允许浏览器下载多个文件</small>', {
        btn: ['确定下载','取消']
    }, function(index){
        layer.close(index);

        var ii = layer.load(2, {shade:[0.1,'#fff']});
        var downloaded = 0;
        var failed = 0;
        var total = codes.length;

        // 使用Promise处理异步下载
        function downloadNext(index) {
            if(index >= codes.length) {
                layer.close(ii);
                var msg = '批量下载完成！';
                if(downloaded > 0) msg += '成功下载 ' + downloaded + ' 个';
                if(failed > 0) msg += '，失败 ' + failed + ' 个';
                layer.msg(msg, {icon: downloaded > 0 ? 1 : 2, time: 3000});
                return;
            }

            var code = codes[index];
            var url = '<?php echo $siteurl; ?>paypage/precode.php?code=' + code;
            var qrUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=' + encodeURIComponent(url);

            // 使用fetch下载
            fetch(qrUrl)
                .then(response => {
                    if (!response.ok) throw new Error('网络错误');
                    return response.blob();
                })
                .then(blob => {
                    var link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = 'precode_' + code + '.png';
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(link.href);
                    downloaded++;
                })
                .catch(error => {
                    console.error('下载失败:', code, error);
                    failed++;
                })
                .finally(() => {
                    // 延时下载下一个
                    setTimeout(function() {
                        downloadNext(index + 1);
                    }, 800);
                });
        }

        // 开始下载
        downloadNext(0);
    });
}
</script>
</body>
</html> 