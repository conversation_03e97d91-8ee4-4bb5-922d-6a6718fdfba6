// api/staff.js - 员工管理相关API (基于测试文件的成功模式)
import { API_LIST } from '@/config/api.js'

const API_BASE = 'http://ceshi.huisas.com'

/**
 * 简化的请求函数，基于测试文件的成功模式
 * @param {Object} options - 请求配置
 * @returns {Promise}
 */
function simpleRequest(options) {
  return new Promise((resolve, reject) => {
    console.log('🚀 简化请求:', options)

    const requestConfig = {
      url: `${API_BASE}${options.url}`,
      method: options.method || 'GET',
      header: {
        'Content-Type': options.contentType || 'application/json',
        ...options.header
      },
      success: (response) => {
        console.log('📥 请求响应:', response)
        if (response.statusCode === 200) {
          let responseData = response.data
          if (typeof responseData === 'string') {
            try {
              responseData = JSON.parse(responseData)
            } catch (e) {
              console.error('JSON解析失败:', e)
              responseData = { error: 'JSON解析失败', raw: response.data }
            }
          }
          resolve(responseData)
        } else {
          reject(new Error(`HTTP ${response.statusCode}: ${response.errMsg}`))
        }
      },
      fail: (error) => {
        console.error('❌ 请求失败:', error)
        reject(error)
      }
    }

    // 添加数据
    if (options.data) {
      if (options.method === 'GET') {
        // GET请求将数据添加到URL参数
        const params = new URLSearchParams(options.data).toString()
        requestConfig.url += (requestConfig.url.includes('?') ? '&' : '?') + params
      } else {
        requestConfig.data = options.data
      }
    }

    console.log('📤 最终请求配置:', requestConfig)
    uni.request(requestConfig)
  })
}

/**
 * 获取员工列表 - 基于测试文件的成功模式
 * @returns {Promise}
 */
export function getStaffList() {
  console.log('🔄 调用员工列表API:', API_LIST.STAFF.LIST)

  // 获取存储的认证信息
  const userToken = uni.getStorageSync('user_token')
  const userUid = uni.getStorageSync('user_uid')
  const csrfToken = uni.getStorageSync('csrf_token')

  console.log('🔑 认证信息:', { userToken: !!userToken, userUid, csrfToken: !!csrfToken })

  // 构建表单数据
  const formData = `user_token=${encodeURIComponent(userToken || '')}&uid=${encodeURIComponent(userUid || '')}&csrf_token=${encodeURIComponent(csrfToken || '')}`

  return simpleRequest({
    url: API_LIST.STAFF.LIST,
    method: 'POST',
    data: formData,
    contentType: 'application/x-www-form-urlencoded'
  })
}

/**
 * 添加员工 - 使用正式staff.php接口
 * @param {Object} staffData - 员工信息
 * @param {string} staffData.name - 员工姓名
 * @param {string} staffData.role - 员工角色
 * @param {string} staffData.account - 登录账号
 * @param {string} staffData.password - 登录密码
 * @param {string} staffData.phone - 手机号
 * @param {string} staffData.email - 邮箱
 * @param {string} staffData.avatar_color - 头像颜色
 * @returns {Promise}
 */
export function addStaff(staffData) {
  console.log('🔄 调用添加员工API:', API_LIST.STAFF.ADD, staffData)

  // 获取存储的认证信息
  const userToken = uni.getStorageSync('user_token')
  const userUid = uni.getStorageSync('user_uid')
  const csrfToken = uni.getStorageSync('csrf_token')

  console.log('🔑 认证信息:', { userToken: !!userToken, userUid, csrfToken: !!csrfToken })

  // 合并员工数据和认证信息
  const allData = {
    ...staffData,
    user_token: userToken || '',
    uid: userUid || '',
    csrf_token: csrfToken || ''
  }

  // 使用表单数据格式，符合后端staff.php的要求
  const formData = Object.keys(allData)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(allData[key] || '')}`)
    .join('&')

  console.log('📤 表单数据:', formData)

  return simpleRequest({
    url: API_LIST.STAFF.ADD,
    method: 'POST',
    data: formData,
    contentType: 'application/x-www-form-urlencoded'
  })
}

/**
 * 编辑员工 - 使用正式staff.php接口
 * @param {Object} staffData - 员工信息
 * @param {number} staffData.staff_id - 员工ID
 * @param {string} staffData.name - 员工姓名
 * @param {string} staffData.role - 员工角色
 * @param {string} staffData.account - 登录账号
 * @param {string} staffData.password - 登录密码
 * @param {string} staffData.phone - 手机号
 * @param {string} staffData.email - 邮箱
 * @param {string} staffData.avatar_color - 头像颜色
 * @returns {Promise}
 */
export function editStaff(staffData) {
  console.log('🔄 调用编辑员工API:', API_LIST.STAFF.EDIT, staffData)

  // 获取存储的认证信息
  const userToken = uni.getStorageSync('user_token')
  const userUid = uni.getStorageSync('user_uid')
  const csrfToken = uni.getStorageSync('csrf_token')

  console.log('🔑 认证信息:', { userToken: !!userToken, userUid, csrfToken: !!csrfToken })

  // 合并员工数据和认证信息
  const allData = {
    ...staffData,
    user_token: userToken || '',
    uid: userUid || '',
    csrf_token: csrfToken || ''
  }

  // 使用表单数据格式
  const formData = Object.keys(allData)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(allData[key] || '')}`)
    .join('&')

  return simpleRequest({
    url: API_LIST.STAFF.EDIT,
    method: 'POST',
    data: formData,
    contentType: 'application/x-www-form-urlencoded'
  })
}

/**
 * 删除员工 - 使用正式staff.php接口
 * @param {number} staffId - 员工ID
 * @returns {Promise}
 */
export function deleteStaff(staffId) {
  console.log('🔄 调用删除员工API:', API_LIST.STAFF.DELETE, staffId)

  // 获取存储的认证信息
  const userToken = uni.getStorageSync('user_token')
  const userUid = uni.getStorageSync('user_uid')
  const csrfToken = uni.getStorageSync('csrf_token')

  console.log('🔑 认证信息:', { userToken: !!userToken, userUid, csrfToken: !!csrfToken })

  // 合并员工ID和认证信息
  const allData = {
    staff_id: staffId,
    user_token: userToken || '',
    uid: userUid || '',
    csrf_token: csrfToken || ''
  }

  // 使用表单数据格式
  const formData = Object.keys(allData)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(allData[key] || '')}`)
    .join('&')

  return simpleRequest({
    url: API_LIST.STAFF.DELETE,
    method: 'POST',
    data: formData,
    contentType: 'application/x-www-form-urlencoded'
  })
}

/**
 * 员工登录
 * @param {Object} loginData - 登录信息
 * @param {string} loginData.account - 登录账号
 * @param {string} loginData.password - 登录密码
 * @param {number} loginData.uid - 商户ID
 * @returns {Promise}
 */
export function staffLogin(loginData) {
  console.log('🔄 调用员工登录API:', API_LIST.STAFF.LOGIN, loginData)

  // 添加CSRF Token
  const csrfToken = uni.getStorageSync('csrf_token')
  if (csrfToken) {
    loginData.csrf_token = csrfToken
  }

  // 将对象转换为表单数据字符串
  const formData = Object.keys(loginData)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(loginData[key] || '')}`)
    .join('&')

  return simpleRequest({
    url: API_LIST.STAFF.LOGIN,
    method: 'POST',
    data: formData,
    contentType: 'application/x-www-form-urlencoded'
  })
}

/**
 * 保存收款码配置（包含员工绑定）
 * @param {Object} configData - 配置信息
 * @param {number} configData.staff_id - 员工ID
 * @param {string} configData.name - 收款码名称
 * @param {string} configData.qr_style - 二维码样式
 * @param {number} configData.amount - 固定金额
 * @param {string} configData.description - 描述
 * @returns {Promise}
 */
export function saveQRConfig(configData) {
  console.log('🔄 调用保存收款码配置API:', API_LIST.STAFF.SAVE_QR_CONFIG, configData)

  // 添加CSRF Token
  const csrfToken = uni.getStorageSync('csrf_token')
  if (csrfToken) {
    configData.csrf_token = csrfToken
  }

  // 将对象转换为表单数据字符串
  const formData = Object.keys(configData)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(configData[key] || '')}`)
    .join('&')

  return simpleRequest({
    url: API_LIST.STAFF.SAVE_QR_CONFIG,
    method: 'POST',
    data: formData,
    contentType: 'application/x-www-form-urlencoded'
  })
}

/**
 * 获取收款码配置列表
 * @returns {Promise}
 */
export function getQRConfigList() {
  console.log('🔄 调用获取收款码配置列表API:', API_LIST.STAFF.GET_QR_CONFIG)

  return simpleRequest({
    url: API_LIST.STAFF.GET_QR_CONFIG,
    method: 'GET',
    contentType: 'application/x-www-form-urlencoded'
  })
}

/**
 * 生成员工收款码URL
 * @param {Object} params - 参数
 * @param {number} params.uid - 商户ID
 * @param {number} params.staff_id - 员工ID
 * @param {number} params.amount - 金额（可选）
 * @returns {string} 收款码URL
 */
export function generateStaffQRUrl(params) {
  const { uid, staff_id, amount } = params
  let url = `http://ceshi.huisas.com/user/pay.php?uid=${uid}`
  
  if (staff_id) {
    url += `&staff_id=${staff_id}`
  }
  
  if (amount && amount > 0) {
    url += `&amount=${amount}`
  }
  
  return url
}

/**
 * 格式化员工信息用于显示
 * @param {Object} staff - 员工信息
 * @returns {Object} 格式化后的员工信息
 */
export function formatStaffInfo(staff) {
  return {
    id: staff.id,
    name: staff.name,
    role: staff.role || '收银员',
    color: staff.color || staff.avatar_color || 'blue',
    phone: staff.phone || '',
    email: staff.email || '',
    addtime: staff.addtime
  }
}

/**
 * 获取员工头像颜色选项
 * @returns {Array} 颜色选项数组
 */
export function getAvatarColorOptions() {
  return [
    { label: '蓝色', value: 'blue' },
    { label: '绿色', value: 'green' },
    { label: '紫色', value: 'purple' },
    { label: '橙色', value: 'orange' },
    { label: '红色', value: 'red' },
    { label: '青色', value: 'cyan' },
    { label: '粉色', value: 'pink' },
    { label: '黄色', value: 'yellow' }
  ]
}

/**
 * 获取员工角色选项
 * @returns {Array} 角色选项数组
 */
export function getStaffRoleOptions() {
  return [
    { label: '店长', value: '店长' },
    { label: '收银员', value: '收银员' },
    { label: '服务员', value: '服务员' },
    { label: '销售员', value: '销售员' },
    { label: '管理员', value: '管理员' }
  ]
}

/**
 * 编辑收款码配置
 * @param {Object} configData - 收款码配置数据
 * @param {number} configData.config_id - 收款码ID
 * @param {string} configData.name - 收款码名称
 * @param {string} configData.style - 收款码样式
 * @param {number} configData.staff_id - 员工ID
 * @param {number} configData.fixed_amount - 固定金额
 * @param {string} configData.description - 描述
 * @returns {Promise}
 */
export function editQRConfig(configData) {
  console.log('🔄 调用编辑收款码API:', configData)

  // 获取存储的认证信息
  const userUid = uni.getStorageSync('user_uid')

  // 字段映射：前端style -> 后端qr_style
  const mappedData = {
    config_id: configData.config_id || configData.id,
    name: configData.name,
    qr_style: configData.style, // 关键修复：映射字段名
    staff_id: configData.staff_id,
    fixed_amount: configData.fixed_amount,
    description: configData.description,
    uid: userUid || ''
  }

  console.log('🔄 字段映射后的数据:', mappedData)

  // 使用表单数据格式
  const formData = Object.keys(mappedData)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(mappedData[key] || '')}`)
    .join('&')

  return simpleRequest({
    url: '/user/staff.php?act=editQRConfig',
    method: 'POST',
    data: formData,
    contentType: 'application/x-www-form-urlencoded'
  })
}

/**
 * 删除收款码配置
 * @param {number} configId - 收款码ID
 * @returns {Promise}
 */
export function deleteQRConfig(configId) {
  console.log('🔄 调用删除收款码API:', configId)

  // 获取存储的认证信息
  const userUid = uni.getStorageSync('user_uid')

  const allData = {
    config_id: configId,
    uid: userUid || ''
  }

  // 使用表单数据格式
  const formData = Object.keys(allData)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(allData[key] || '')}`)
    .join('&')

  return simpleRequest({
    url: '/user/staff.php?act=deleteQRConfig',
    method: 'POST',
    data: formData,
    contentType: 'application/x-www-form-urlencoded'
  })
}

/**
 * 获取收款码配置列表（更新版本）
 * @returns {Promise}
 */
export function getQRConfigListNew() {
  console.log('🔄 调用获取收款码配置列表API（新版本）')

  // 获取存储的认证信息
  const userUid = uni.getStorageSync('user_uid')

  const allData = {
    uid: userUid || ''
  }

  // 使用表单数据格式
  const formData = Object.keys(allData)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(allData[key] || '')}`)
    .join('&')

  return simpleRequest({
    url: '/user/staff.php?act=getQRConfigList',
    method: 'POST',
    data: formData,
    contentType: 'application/x-www-form-urlencoded'
  })
}

/**
 * 创建收款码配置（更新版本）
 * @param {Object} configData - 收款码配置数据
 * @param {string} configData.name - 收款码名称
 * @param {string} configData.style - 收款码样式
 * @param {number} configData.staff_id - 员工ID
 * @param {number} configData.fixed_amount - 固定金额
 * @param {string} configData.description - 描述
 * @param {string} configData.precode - 预制码
 * @returns {Promise}
 */
export function createQRConfig(configData) {
  console.log('🔄 调用创建收款码API:', configData)

  // 获取存储的认证信息
  const userUid = uni.getStorageSync('user_uid')

  // 字段映射：前端style -> 后端qr_style
  const mappedData = {
    name: configData.name,
    qr_style: configData.style, // 关键修复：映射字段名
    staff_id: configData.staff_id,
    fixed_amount: configData.fixed_amount,
    description: configData.description,
    precode: configData.precode,
    uid: userUid || ''
  }

  console.log('🔄 字段映射后的数据:', mappedData)

  // 使用表单数据格式
  const formData = Object.keys(mappedData)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(mappedData[key] || '')}`)
    .join('&')

  return simpleRequest({
    url: '/user/staff.php?act=saveQRConfig',
    method: 'POST',
    data: formData,
    contentType: 'application/x-www-form-urlencoded'
  })
}

/**
 * 绑定预制码
 * @param {Object} bindData - 绑定数据
 * @param {string} bindData.precode - 预制码
 * @param {string} bindData.name - 收款码名称
 * @param {string} bindData.style - 收款码样式
 * @param {number} bindData.staff_id - 员工ID
 * @param {string} bindData.description - 描述
 * @returns {Promise}
 */
export function bindPreCode(bindData) {
  console.log('🔄 调用绑定预制码API:', bindData)

  // 获取存储的认证信息
  const userUid = uni.getStorageSync('user_uid')

  // 字段映射
  const mappedData = {
    precode: bindData.precode,
    name: bindData.name,
    style: bindData.style,
    staff_id: bindData.staff_id,
    description: bindData.description,
    uid: userUid || ''
  }

  console.log('🔄 绑定预制码数据:', mappedData)

  // 使用表单数据格式
  const formData = Object.keys(mappedData)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(mappedData[key] || '')}`)
    .join('&')

  return simpleRequest({
    url: '/user/staff.php?act=bindPreCode',
    method: 'POST',
    data: formData,
    contentType: 'application/x-www-form-urlencoded'
  })
}

/**
 * 获取可用预制码列表
 * @param {number} page - 页码
 * @returns {Promise}
 */
export function getAvailablePreCodes(page = 1) {
  console.log('🔄 调用获取可用预制码API:', page)

  return simpleRequest({
    url: `/user/staff.php?act=getAvailablePreCodes&page=${page}`,
    method: 'GET',
    contentType: 'application/x-www-form-urlencoded'
  })
}

/**
 * 验证预制码是否有效
 * @param {string} precode - 预制码
 * @returns {Promise}
 */
export function verifyPreCode(precode) {
  console.log('🔄 调用验证预制码API:', precode)

  const formData = `precode=${encodeURIComponent(precode)}`

  return simpleRequest({
    url: '/user/staff.php?act=verifyPreCode',
    method: 'POST',
    data: formData,
    contentType: 'application/x-www-form-urlencoded'
  })
}

// 默认导出
export default {
  getStaffList,
  addStaff,
  editStaff,
  deleteStaff,
  staffLogin,
  saveQRConfig,
  getQRConfigList,
  generateStaffQRUrl,
  formatStaffInfo,
  getAvatarColorOptions,
  getStaffRoleOptions,
  // 新增的收款码管理API
  editQRConfig,
  deleteQRConfig,
  getQRConfigListNew,
  createQRConfig,
  // 预制码相关API
  bindPreCode,
  getAvailablePreCodes,
  verifyPreCode
}
