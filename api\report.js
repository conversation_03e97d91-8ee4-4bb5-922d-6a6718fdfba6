/**
 * 报表数据API接口
 */
import { request } from '@/utils/request'

/**
 * 获取报表统计数据
 * @param {Object} params - 查询参数
 * @param {string} params.starttime - 开始时间 YYYY-MM-DD
 * @param {string} params.endtime - 结束时间 YYYY-MM-DD
 * @param {number} params.paytype - 支付方式ID (可选)
 * @param {number} params.dstatus - 订单状态 (可选)
 * @returns {Promise} 请求结果
 */
export function getReportStatistics(params = {}) {
  return request({
    url: '/user/ajax2.php?act=statistics',
    method: 'POST',
    data: params
  })
}

/**
 * 获取商户基础信息和今日统计
 * @returns {Promise} 请求结果
 */
export function getMerchantBasicInfo() {
  const merchantId = uni.getStorageSync('user_uid') || uni.getStorageSync('merchantId')
  const merchantKey = uni.getStorageSync('user_key') || uni.getStorageSync('merchantKey')
  
  return request({
    url: '/api.php',
    method: 'GET',
    data: {
      act: 'query',
      pid: merchantId,
      key: merchantKey
    }
  })
}

/**
 * 获取订单列表数据 (用于趋势分析)
 * @param {Object} params - 查询参数
 * @param {string} params.starttime - 开始时间 YYYY-MM-DD
 * @param {string} params.endtime - 结束时间 YYYY-MM-DD
 * @param {number} params.offset - 偏移量
 * @param {number} params.limit - 限制数量
 * @returns {Promise} 请求结果
 */
export function getOrderListForReport(params = {}) {
  return request({
    url: '/user/ajax2.php?act=orderList',
    method: 'POST',
    data: {
      offset: 0,
      limit: 1000, // 获取更多数据用于分析
      ...params
    }
  })
}

/**
 * 获取支付方式统计数据
 * @param {Object} params - 查询参数
 * @param {string} params.starttime - 开始时间 YYYY-MM-DD
 * @param {string} params.endtime - 结束时间 YYYY-MM-DD
 * @returns {Promise} 请求结果
 */
export function getPaymentMethodStats(params = {}) {
  // 先获取统计数据，然后分别查询各支付方式
  return getReportStatistics(params).then(result => {
    if (result.code === 0) {
      // 获取各支付方式的详细数据
      const promises = [
        getReportStatistics({ ...params, paytype: 1 }), // 支付宝
        getReportStatistics({ ...params, paytype: 2 }), // 微信支付
        getReportStatistics({ ...params, paytype: 3 }), // QQ钱包
        getReportStatistics({ ...params, paytype: 4 })  // 云闪付
      ]
      
      return Promise.all(promises).then(results => {
        const paymentData = []
        const paymentNames = ['支付宝', '微信支付', 'QQ钱包', '云闪付']
        
        results.forEach((res, index) => {
          if (res.code === 0 && parseFloat(res.data.successMoney) > 0) {
            paymentData.push({
              name: paymentNames[index],
              amount: parseFloat(res.data.successMoney),
              count: parseInt(res.data.successCount),
              type: index + 1
            })
          }
        })
        
        return {
          code: 0,
          data: paymentData
        }
      })
    }
    return result
  })
}

/**
 * 获取每日收入趋势数据
 * @param {Object} params - 查询参数
 * @param {string} params.starttime - 开始时间 YYYY-MM-DD
 * @param {string} params.endtime - 结束时间 YYYY-MM-DD
 * @returns {Promise} 请求结果
 */
export function getDailyIncomeStats(params = {}) {
  return getOrderListForReport(params).then(result => {
    if (result.code === 0 || (result.total !== undefined && result.rows)) {
      const orders = result.rows || []
      const dailyStats = {}
      
      // 按日期分组统计
      orders.forEach(order => {
        if (order.status == 1) { // 只统计已支付订单
          const date = order.addtime.split(' ')[0] // 提取日期部分
          if (!dailyStats[date]) {
            dailyStats[date] = {
              amount: 0,
              count: 0
            }
          }
          dailyStats[date].amount += parseFloat(order.realmoney || order.money || 0)
          dailyStats[date].count += 1
        }
      })
      
      // 转换为数组格式
      const dailyData = Object.keys(dailyStats).sort().map(date => ({
        date,
        amount: dailyStats[date].amount.toFixed(2),
        count: dailyStats[date].count
      }))
      
      return {
        code: 0,
        data: dailyData
      }
    }
    return { code: -1, msg: '获取数据失败' }
  })
}

/**
 * 获取时段分析数据
 * @param {Object} params - 查询参数
 * @param {string} params.starttime - 开始时间 YYYY-MM-DD
 * @param {string} params.endtime - 结束时间 YYYY-MM-DD
 * @returns {Promise} 请求结果
 */
export function getHourlyStats(params = {}) {
  return getOrderListForReport(params).then(result => {
    if (result.code === 0 || (result.total !== undefined && result.rows)) {
      const orders = result.rows || []
      const hourlyStats = {}
      
      // 初始化24小时数据
      for (let i = 0; i < 24; i++) {
        hourlyStats[i] = { amount: 0, count: 0 }
      }
      
      // 按小时分组统计
      orders.forEach(order => {
        if (order.status == 1) { // 只统计已支付订单
          const hour = parseInt(order.addtime.split(' ')[1].split(':')[0])
          hourlyStats[hour].amount += parseFloat(order.realmoney || order.money || 0)
          hourlyStats[hour].count += 1
        }
      })
      
      // 转换为数组格式
      const hourlyData = Object.keys(hourlyStats).map(hour => ({
        hour: parseInt(hour),
        hourLabel: `${hour}:00`,
        amount: hourlyStats[hour].amount.toFixed(2),
        count: hourlyStats[hour].count
      }))
      
      return {
        code: 0,
        data: hourlyData
      }
    }
    return { code: -1, msg: '获取数据失败' }
  })
}

/**
 * 获取客户统计数据 (基于用户标识分析)
 * @param {Object} params - 查询参数
 * @param {string} params.starttime - 开始时间 YYYY-MM-DD
 * @param {string} params.endtime - 结束时间 YYYY-MM-DD
 * @returns {Promise} 请求结果
 */
export function getCustomerStats(params = {}) {
  return getOrderListForReport(params).then(result => {
    if (result.code === 0 || (result.total !== undefined && result.rows)) {
      const orders = result.rows || []
      const customerMap = {}

      // 按买家标识分组统计 (支持openid、appid、user_id等)
      orders.forEach(order => {
        if (order.status == 1 && order.buyer) { // 只统计已支付且有买家信息的订单
          const buyerId = order.buyer.trim()

          // 过滤掉无效的买家标识
          if (buyerId && buyerId !== 'null' && buyerId !== 'undefined' && buyerId.length > 3) {
            if (!customerMap[buyerId]) {
              customerMap[buyerId] = {
                count: 0,
                amount: 0,
                firstOrder: order.addtime,
                lastOrder: order.addtime,
                orders: []
              }
            }

            customerMap[buyerId].count += 1
            customerMap[buyerId].amount += parseFloat(order.realmoney || order.money || 0)
            customerMap[buyerId].lastOrder = order.addtime
            customerMap[buyerId].orders.push({
              trade_no: order.trade_no,
              money: parseFloat(order.realmoney || order.money || 0),
              addtime: order.addtime
            })
          }
        }
      })

      const customers = Object.values(customerMap)

      // 分类客户
      const newCustomers = customers.filter(c => c.count === 1).length
      const returningCustomers = customers.filter(c => c.count >= 2 && c.count <= 5).length
      const loyalCustomers = customers.filter(c => c.count > 5).length
      const totalCustomers = customers.length

      // 计算忠诚度指标
      const loyaltyRate = totalCustomers > 0 ? (((returningCustomers + loyalCustomers) / totalCustomers) * 100).toFixed(1) : 0
      const avgOrdersPerCustomer = totalCustomers > 0 ? (orders.filter(o => o.status == 1).length / totalCustomers).toFixed(1) : 0
      const avgAmountPerCustomer = totalCustomers > 0 ? (customers.reduce((sum, c) => sum + c.amount, 0) / totalCustomers).toFixed(2) : 0

      // 客户价值分析
      const highValueCustomers = customers.filter(c => c.amount > 1000).length
      const mediumValueCustomers = customers.filter(c => c.amount >= 100 && c.amount <= 1000).length
      const lowValueCustomers = customers.filter(c => c.amount < 100).length

      return {
        code: 0,
        data: {
          // 基础统计
          newCustomers,
          returningCustomers,
          loyalCustomers,
          totalCustomers,
          loyaltyRate: parseFloat(loyaltyRate),

          // 深度分析
          avgOrdersPerCustomer: parseFloat(avgOrdersPerCustomer),
          avgAmountPerCustomer: parseFloat(avgAmountPerCustomer),

          // 客户价值分布
          highValueCustomers,
          mediumValueCustomers,
          lowValueCustomers,

          // 详细客户列表 (前10名)
          topCustomers: customers
            .sort((a, b) => b.amount - a.amount)
            .slice(0, 10)
            .map(c => ({
              buyerId: Object.keys(customerMap).find(key => customerMap[key] === c),
              orderCount: c.count,
              totalAmount: c.amount.toFixed(2),
              firstOrder: c.firstOrder,
              lastOrder: c.lastOrder
            }))
        }
      }
    }
    return { code: -1, msg: '获取数据失败' }
  })
}

/**
 * 获取客户忠诚度详细分析
 * @param {Object} params - 查询参数
 * @param {string} params.starttime - 开始时间 YYYY-MM-DD
 * @param {string} params.endtime - 结束时间 YYYY-MM-DD
 * @returns {Promise} 请求结果
 */
export function getCustomerLoyaltyAnalysis(params = {}) {
  return getOrderListForReport(params).then(result => {
    if (result.code === 0 || (result.total !== undefined && result.rows)) {
      const orders = result.rows || []
      const customerMap = {}

      // 构建客户画像
      orders.forEach(order => {
        if (order.status == 1 && order.buyer) {
          const buyerId = order.buyer.trim()

          if (buyerId && buyerId !== 'null' && buyerId !== 'undefined' && buyerId.length > 3) {
            if (!customerMap[buyerId]) {
              customerMap[buyerId] = {
                buyerId,
                orderCount: 0,
                totalAmount: 0,
                firstOrderTime: order.addtime,
                lastOrderTime: order.addtime,
                orders: [],
                avgDaysBetweenOrders: 0,
                customerType: 'new', // new, returning, loyal, vip
                riskLevel: 'low' // low, medium, high (流失风险)
              }
            }

            const customer = customerMap[buyerId]
            customer.orderCount += 1
            customer.totalAmount += parseFloat(order.realmoney || order.money || 0)
            customer.lastOrderTime = order.addtime
            customer.orders.push({
              trade_no: order.trade_no,
              money: parseFloat(order.realmoney || order.money || 0),
              addtime: order.addtime,
              type: order.type
            })
          }
        }
      })

      // 分析客户行为
      const customers = Object.values(customerMap)
      const now = new Date()

      customers.forEach(customer => {
        // 计算平均订单间隔
        if (customer.orderCount > 1) {
          const firstTime = new Date(customer.firstOrderTime)
          const lastTime = new Date(customer.lastOrderTime)
          const daysDiff = Math.ceil((lastTime - firstTime) / (1000 * 60 * 60 * 24))
          customer.avgDaysBetweenOrders = Math.ceil(daysDiff / (customer.orderCount - 1))
        }

        // 客户分类
        if (customer.orderCount === 1) {
          customer.customerType = 'new'
        } else if (customer.orderCount >= 2 && customer.orderCount <= 5) {
          customer.customerType = 'returning'
        } else if (customer.orderCount > 5 && customer.totalAmount < 1000) {
          customer.customerType = 'loyal'
        } else if (customer.orderCount > 5 && customer.totalAmount >= 1000) {
          customer.customerType = 'vip'
        }

        // 流失风险评估
        const daysSinceLastOrder = Math.ceil((now - new Date(customer.lastOrderTime)) / (1000 * 60 * 60 * 24))
        if (customer.orderCount === 1) {
          customer.riskLevel = daysSinceLastOrder > 30 ? 'high' : 'medium'
        } else {
          const expectedNextOrder = customer.avgDaysBetweenOrders * 2
          if (daysSinceLastOrder > expectedNextOrder) {
            customer.riskLevel = 'high'
          } else if (daysSinceLastOrder > customer.avgDaysBetweenOrders) {
            customer.riskLevel = 'medium'
          } else {
            customer.riskLevel = 'low'
          }
        }
      })

      // 统计分析
      const stats = {
        total: customers.length,
        new: customers.filter(c => c.customerType === 'new').length,
        returning: customers.filter(c => c.customerType === 'returning').length,
        loyal: customers.filter(c => c.customerType === 'loyal').length,
        vip: customers.filter(c => c.customerType === 'vip').length,
        highRisk: customers.filter(c => c.riskLevel === 'high').length,
        mediumRisk: customers.filter(c => c.riskLevel === 'medium').length,
        lowRisk: customers.filter(c => c.riskLevel === 'low').length
      }

      // 计算关键指标
      const totalRevenue = customers.reduce((sum, c) => sum + c.totalAmount, 0)
      const avgCustomerValue = customers.length > 0 ? (totalRevenue / customers.length) : 0
      const retentionRate = customers.length > 0 ? ((stats.returning + stats.loyal + stats.vip) / customers.length * 100) : 0

      return {
        code: 0,
        data: {
          summary: {
            totalCustomers: stats.total,
            totalRevenue: totalRevenue.toFixed(2),
            avgCustomerValue: avgCustomerValue.toFixed(2),
            retentionRate: retentionRate.toFixed(1)
          },
          distribution: stats,
          topCustomers: customers
            .sort((a, b) => b.totalAmount - a.totalAmount)
            .slice(0, 20)
            .map(c => ({
              buyerId: c.buyerId.length > 20 ? c.buyerId.substring(0, 20) + '...' : c.buyerId,
              orderCount: c.orderCount,
              totalAmount: c.totalAmount.toFixed(2),
              avgOrderAmount: (c.totalAmount / c.orderCount).toFixed(2),
              customerType: c.customerType,
              riskLevel: c.riskLevel,
              daysSinceLastOrder: Math.ceil((now - new Date(c.lastOrderTime)) / (1000 * 60 * 60 * 24)),
              avgDaysBetweenOrders: c.avgDaysBetweenOrders
            })),
          riskCustomers: customers
            .filter(c => c.riskLevel === 'high' && c.customerType !== 'new')
            .sort((a, b) => b.totalAmount - a.totalAmount)
            .slice(0, 10)
            .map(c => ({
              buyerId: c.buyerId.length > 20 ? c.buyerId.substring(0, 20) + '...' : c.buyerId,
              orderCount: c.orderCount,
              totalAmount: c.totalAmount.toFixed(2),
              daysSinceLastOrder: Math.ceil((now - new Date(c.lastOrderTime)) / (1000 * 60 * 60 * 24)),
              expectedReturn: c.avgDaysBetweenOrders
            }))
        }
      }
    }
    return { code: -1, msg: '获取数据失败' }
  })
}

/**
 * 导出报表数据
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求结果
 */
export function exportReportData(params = {}) {
  // 这里可以实现数据导出功能
  return Promise.resolve({
    code: 0,
    msg: '导出功能开发中...'
  })
}
