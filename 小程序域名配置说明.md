# 小程序后台域名配置说明

## 🔧 需要在微信小程序后台配置的域名

### 1. request合法域名
```
https://api.2dcode.biz
https://ceshi.huisas.com
```

### 2. downloadFile合法域名
```
https://api.2dcode.biz
https://img.alicdn.com
```

### 3. uploadFile合法域名
```
https://ceshi.huisas.com
https://img.alicdn.com
```

### 4. socket合法域名
```
wss://ceshi.huisas.com
```

## 📝 配置步骤

1. 登录微信小程序后台
2. 进入 **开发** → **开发设置**
3. 找到 **服务器域名** 配置
4. 分别在对应的域名类型中添加上述域名
5. 点击 **保存并提交** 

## ⚠️ 注意事项

- 域名必须是 https 协议（socket是wss协议）
- 每个月只能修改5次域名配置
- 配置后需要重新编译小程序才能生效
- 建议一次性配置完所有需要的域名

## 🔍 验证方法

配置完成后，在小程序开发者工具中：
1. 清除缓存并重新编译
2. 查看控制台是否还有域名相关错误
3. 测试二维码生成功能是否正常

## 🚀 当前使用的二维码API

1. **api.2dcode.biz** - 草料二维码API，国内最稳定，无需VPN
   - 支持多种纠错级别（L、M、Q、H）
   - 支持自定义边框大小
   - 国内访问速度快，稳定性高

## ✅ 优势

- ✅ **国内访问**：无需VPN，访问速度快
- ✅ **稳定可靠**：草料二维码是国内知名服务商
- ✅ **功能丰富**：支持多种参数配置
- ✅ **免费使用**：API调用无限制
