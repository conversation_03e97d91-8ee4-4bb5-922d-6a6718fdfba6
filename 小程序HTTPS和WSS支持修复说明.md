# 🔒 小程序HTTPS和WSS支持修复说明

## 📋 问题描述

小程序要求所有网络请求必须使用HTTPS协议，WebSocket连接必须使用WSS协议。当前项目存在以下问题：

1. **HTTP请求问题** - 部分配置仍使用HTTP协议
2. **WebSocket连接问题** - WebSocket使用WS协议，小程序无法连接
3. **小程序页面空白** - 可能由于网络请求失败导致
4. **SSL证书配置** - 后端WebSocket服务器需要支持WSS

## 🛠️ 修复方案

### 1. 前端配置修改

#### 1.1 API请求配置 ✅
已修改以下文件支持HTTPS：

**config/index.js**
```javascript
// 开发环境和生产环境都使用HTTPS
serverUrl: 'https://ceshi.huisas.com'
```

**vite.config.js**
```javascript
proxy: {
  '/api.php': {
    target: 'https://ceshi.huisas.com',
    changeOrigin: true,
    secure: true  // 启用SSL验证
  }
}
```

**manifest.json**
```json
"proxy": {
  "/api.php": {
    "target": "https://ceshi.huisas.com",
    "changeOrigin": true,
    "secure": true
  }
}
```

#### 1.2 WebSocket配置修改 ✅
已修改以下文件支持WSS：

**utils/websocketService.js**
```javascript
config: {
  host: 'ceshi.huisas.com',
  port: 8080,
  protocol: 'wss', // 使用WSS协议支持小程序
  app_key: 'payment_websocket_2024'
}
```

**utils/paymentWebSocket.js**
```javascript
// 动态构建WebSocket URL
const wsUrl = `${this.config.protocol}://${this.config.host}:${this.config.port}`
```

**mixins/voicePlayerMixin.js**
```javascript
wsConfig: {
  host: 'ceshi.huisas.com',
  port: 8080,
  protocol: 'wss', // 使用WSS协议支持小程序
}
```

**mixins/websocketMixin.js**
```javascript
const wsUrl = `wss://ceshi.huisas.com:8080?id=${uid}`
```

**utils/websocketManager.js**
```javascript
this.wsUrl = 'wss://ceshi.huisas.com:8080'
```

#### 1.3 小程序配置优化 ✅
**manifest.json - 小程序配置**
```json
"mp-weixin": {
  "appid": "wx163aae36c89381f8",
  "setting": {
    "urlCheck": false
  },
  "networkTimeout": {
    "request": 60000,
    "connectSocket": 60000,
    "uploadFile": 60000,
    "downloadFile": 60000
  }
}
```

#### 1.4 小程序启动逻辑优化 ✅
**App.vue**
```javascript
onLaunch: function() {
  // 小程序启动时检查登录状态
  this.checkLoginOnLaunch()
},

checkLoginOnLaunch() {
  // #ifdef MP-WEIXIN
  const userToken = uni.getStorageSync('user_token')
  const userUid = uni.getStorageSync('user_uid')
  
  if (userToken && userUid) {
    // 延迟跳转，确保小程序完全启动
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index'
      })
    }, 100)
  }
  // #endif
}
```

### 2. 后端SSL配置检查

#### 2.1 WebSocket服务器SSL配置 ✅
**ceshi.huisas.com/swoole_websocket_ssl_config.php**
```php
$sslConfig = [
  'ssl_enabled' => true,
  'ssl_cert_file' => '/www/server/panel/vhost/cert/ceshi.huisas.com/fullchain.pem',
  'ssl_key_file' => '/www/server/panel/vhost/cert/ceshi.huisas.com/privkey.pem',
  'ssl_protocols' => SWOOLE_SSL_TLSv1_2 | SWOOLE_SSL_TLSv1_3,
];

$serverConfig = [
  'host' => '0.0.0.0',
  'port' => 8080,
  'open_ssl' => $sslConfig['ssl_enabled'],
  'ssl_cert_file' => $sslConfig['ssl_cert_file'],
  'ssl_key_file' => $sslConfig['ssl_key_file'],
];
```

#### 2.2 SSL证书检查
配置文件会自动检查SSL证书是否存在：
- 如果证书文件不存在，会自动禁用SSL并提示
- 如果证书正常，会启用WSS协议

### 3. 测试和验证

#### 3.1 创建测试页面 ✅
创建了 `pages/test/miniprogram-test.vue` 用于诊断小程序问题：
- 系统信息检查
- 登录状态检查  
- 网络连接测试
- WebSocket连接测试
- 页面跳转测试

#### 3.2 测试步骤
1. **编译小程序**
   ```bash
   npm run dev:mp-weixin
   ```

2. **微信开发者工具测试**
   - 导入项目到微信开发者工具
   - 检查网络请求是否使用HTTPS
   - 检查WebSocket是否使用WSS
   - 测试页面跳转和功能

3. **真机测试**
   - 上传体验版到微信小程序
   - 在真实设备上测试所有功能

## 🔍 问题排查

### 1. 小程序页面空白
可能原因：
- 网络请求失败（HTTP协议被拒绝）
- WebSocket连接失败（WS协议被拒绝）
- 页面路由配置问题
- 登录状态检查逻辑问题

### 2. WebSocket连接失败
检查项目：
- 后端SSL证书是否正确配置
- 端口8080是否支持WSS
- 防火墙是否允许WSS连接
- 小程序域名白名单配置

### 3. API请求失败
检查项目：
- 服务器是否支持HTTPS
- SSL证书是否有效
- 代理配置是否正确
- 小程序域名白名单配置

## 📝 后续步骤

1. **测试当前修改**
   - 编译小程序并在微信开发者工具中测试
   - 检查所有网络请求是否正常

2. **服务器配置验证**
   - 确认SSL证书配置正确
   - 测试WSS连接是否可用

3. **小程序域名配置**
   - 在微信小程序后台配置服务器域名
   - 添加 `https://ceshi.huisas.com` 到request合法域名
   - 添加 `wss://ceshi.huisas.com` 到socket合法域名

4. **功能测试**
   - 登录功能测试
   - 支付通知功能测试
   - 语音播报功能测试

## ✅ 修改总结

已完成的修改：
- ✅ 所有API请求配置改为HTTPS
- ✅ 所有WebSocket连接改为WSS
- ✅ 小程序配置优化
- ✅ 启动逻辑优化
- ✅ 创建测试页面

需要验证的项目：
- 🔍 后端SSL证书配置
- 🔍 WSS连接可用性
- 🔍 小程序功能完整性
- 🔍 真机测试结果
