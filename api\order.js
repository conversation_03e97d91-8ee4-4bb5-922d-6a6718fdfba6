// api/order.js - 订单相关API
import { get, post } from '@/utils/request';
import { signParams } from '@/utils/sign';
import { API_LIST } from '@/config/api';
import config from '@/config/index';

/**
 * 获取订单列表
 * @param {Number} limit - 每页记录数
 * @param {Number} offset - 偏移量
 * @param {Number} status - 订单状态(可选): 0-未支付, 1-已支付
 * @returns {Promise} 请求结果
 */
export function getOrderList(limit = 10, offset = 0, status = null) {
  const merchantId = uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('merchantKey') || config.merchant.key;
  
  const timestamp = Math.floor(Date.now() / 1000).toString();
  
  // 构建参数
  const params = {
    pid: merchantId,
    limit: limit,
    offset: offset,
    timestamp: timestamp,
    sign_type: 'MD5'
  };
  
  // 如果指定了状态，添加状态参数
  if (status !== null) {
    params.status = status;
  }
  
  // 添加签名
  params.sign = signParams(params, merchantKey);
  
  // 使用新版API
  return post('/api/merchant/orders', params);
}

/**
 * 获取订单列表 (旧版API，保留兼容)
 * @param {Number} limit - 每页记录数
 * @param {Number} offset - 偏移量
 * @returns {Promise} 请求结果 
 */
export function getOrderListLegacy(limit = 10, offset = 0) {
  const merchantId = uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('merchantKey') || config.merchant.key;
  
  return get('/api.php', {
    act: 'orders',
    pid: merchantId,
    key: merchantKey,
    limit: limit,
    offset: offset
  });
}

/**
 * 查询单个订单
 * @param {String} orderId - 商户订单号
 * @param {String} tradeNo - 平台订单号 (可选)
 * @returns {Promise} 请求结果
 */
export function getOrder(orderId, tradeNo) {
  const merchantId = uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('merchantKey') || config.merchant.key;
  
  const timestamp = Math.floor(Date.now() / 1000).toString();
  
  // 构建参数
  const params = {
    pid: merchantId,
    timestamp: timestamp,
    sign_type: 'MD5'
  };
  
  // 订单号二选一
  if (orderId) {
    params.out_trade_no = orderId;
  } else if (tradeNo) {
    params.trade_no = tradeNo;
  }
  
  // 添加签名
  params.sign = signParams(params, merchantKey);
  
  // 使用新版API
  return post('/api/pay/query', params);
}

/**
 * 查询单个订单 (旧版API，保留兼容)
 * @param {String} orderId - 商户订单号
 * @returns {Promise} 请求结果
 */
export function getOrderLegacy(orderId) {
  const merchantId = uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('merchantKey') || config.merchant.key;
  
  return get('/api.php', {
    act: 'order',
    pid: merchantId,
    key: merchantKey,
    out_trade_no: orderId
  });
}

/**
 * 申请退款
 * @param {String} orderId - 商户订单号
 * @param {String} tradeNo - 平台订单号 (可选)
 * @param {Number} amount - 退款金额(元)
 * @param {String} refundId - 商户退款单号 (可选)
 * @returns {Promise} 请求结果
 */
export function refundOrder(orderId, tradeNo, amount, refundId = '') {
  const merchantId = uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('merchantKey') || config.merchant.key;
  
  const timestamp = Math.floor(Date.now() / 1000).toString();
  
  // 构建参数
  const params = {
    pid: merchantId,
    money: amount.toString(),
    timestamp: timestamp,
    sign_type: 'MD5'
  };
  
  // 订单号二选一
  if (orderId) {
    params.out_trade_no = orderId;
  } else if (tradeNo) {
    params.trade_no = tradeNo;
  }
  
  // 商户退款单号(可选)
  if (refundId) {
    params.out_refund_no = refundId;
  }
  
  // 添加签名
  params.sign = signParams(params, merchantKey);
  
  // 使用新版API
  return post('/api/pay/refund', params);
}

/**
 * 申请退款 (旧版API，保留兼容)
 * @param {String} orderId - 商户订单号
 * @param {Number} amount - 退款金额(元)
 * @returns {Promise} 请求结果
 */
export function refundOrderLegacy(orderId, amount) {
  const merchantId = uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('merchantKey') || config.merchant.key;
  
  return get('/api.php', {
    act: 'refund',
    pid: merchantId,
    key: merchantKey,
    out_trade_no: orderId,
    money: amount
  });
}

/**
 * 获取今日订单统计
 * @returns {Promise} 请求结果
 */
export function getTodayOrderStats() {
  // 这个需要从商户信息API中获取
  return import('./merchant').then(({ getMerchantInfo }) => {
    return getMerchantInfo().then(result => {
      if (result.code === 1) {
        return {
          code: 0,
          data: {
            count: result.orders_today || 0,
            amount: result.orders_today_all || '0.00'
          }
        };
      }
      return Promise.reject(new Error('获取今日订单统计失败'));
    });
  });
}

/**
 * 解析订单状态文本
 * @param {Number} status - 订单状态码
 * @returns {String} 状态文本
 */
export function getOrderStatusText(status) {
  const statusMap = {
    0: '未支付',
    1: '已支付',
    2: '已退款',
    3: '已关闭'
  };
  return statusMap[status] || '未知状态';
}

// ========== 新增的订单记录页面专用API ==========

/**
 * 获取订单列表 (用于订单记录页面)
 * @param {Object} params - 查询参数
 * @param {number} params.offset - 偏移量
 * @param {number} params.limit - 每页数量
 * @param {string} params.starttime - 开始时间 YYYY-MM-DD
 * @param {string} params.endtime - 结束时间 YYYY-MM-DD
 * @param {number} params.dstatus - 订单状态 (-1:全部, 0:未支付, 1:已支付, 2:已退款, 3:已冻结)
 * @param {number} params.paytype - 支付方式ID
 * @param {number} params.type - 搜索类型 (1:系统订单号, 2:商户订单号, 3:商品名称等)
 * @param {string} params.kw - 搜索关键词
 * @returns {Promise} 请求结果
 */
export function getBillOrderList(params = {}) {
  const defaultParams = {
    offset: 0,
    limit: 20,
    dstatus: -1 // 默认查询所有状态
  };

  return post(API_LIST.ORDER.LIST, { ...defaultParams, ...params });
}

/**
 * 获取订单详情 (用于订单记录页面)
 * @param {string} tradeNo - 订单号
 * @returns {Promise} 请求结果
 */
export function getBillOrderDetail(tradeNo) {
  return post(API_LIST.ORDER.DETAIL, { trade_no: tradeNo });
}

/**
 * 获取订单统计数据 (用于订单记录页面)
 * @param {Object} params - 查询参数
 * @param {string} params.starttime - 开始时间 YYYY-MM-DD
 * @param {string} params.endtime - 结束时间 YYYY-MM-DD
 * @param {number} params.paytype - 支付方式ID
 * @param {number} params.dstatus - 订单状态
 * @returns {Promise} 请求结果
 */
export function getBillOrderStatistics(params = {}) {
  return post(API_LIST.ORDER.STATISTICS, params);
}

/**
 * 获取资金明细列表
 * @param {Object} params - 查询参数
 * @param {number} params.offset - 偏移量
 * @param {number} params.limit - 每页数量
 * @param {number} params.type - 搜索类型 (1:操作类型, 2:变更金额, 3:关联订单号)
 * @param {string} params.kw - 搜索关键词
 * @returns {Promise} 请求结果
 */
export function getRecordList(params = {}) {
  const defaultParams = {
    offset: 0,
    limit: 20
  };

  return post(API_LIST.ORDER.RECORD_LIST, { ...defaultParams, ...params });
}

/**
 * 订单退款查询
 * @param {string} tradeNo - 订单号
 * @returns {Promise} 请求结果
 */
export function queryRefund(tradeNo) {
  return post('/user/ajax2.php?act=refund_query', { trade_no: tradeNo });
}

/**
 * 提交订单退款
 * @param {string} tradeNo - 订单号
 * @param {string} money - 退款金额
 * @param {string} pwd - 登录密码
 * @returns {Promise} 请求结果
 */
export function submitRefund(tradeNo, money, pwd) {
  return post('/user/ajax2.php?act=refund_submit', {
    trade_no: tradeNo,
    money: money,
    pwd: pwd
  });
}

/**
 * 订单补单通知
 * @param {string} tradeNo - 订单号
 * @param {boolean} isReturn - 是否同步通知
 * @returns {Promise} 请求结果
 */
export function notifyOrder(tradeNo, isReturn = false) {
  const params = { trade_no: tradeNo };
  if (isReturn) {
    params.isreturn = 1;
  }

  return post('/user/ajax2.php?act=notify', params);
}

// ========== 辅助函数 ==========

/**
 * 格式化订单状态文本 (扩展版)
 * @param {string|number} status - 订单状态
 * @returns {string} 状态文本
 */
export function formatOrderStatus(status) {
  const statusMap = {
    '0': '未支付',
    '1': '已支付',
    '2': '已退款',
    '3': '已冻结',
    '4': '预授权'
  };

  return statusMap[status.toString()] || '未知状态';
}

/**
 * 格式化订单状态颜色
 * @param {string|number} status - 订单状态
 * @returns {string} 颜色值
 */
export function formatOrderStatusColor(status) {
  const colorMap = {
    '0': '#FF9500', // 橙色 - 未支付
    '1': '#4CD964', // 绿色 - 已支付
    '2': '#FF3B30', // 红色 - 已退款
    '3': '#8E8E93', // 灰色 - 已冻结
    '4': '#FF9500'  // 橙色 - 预授权
  };

  return colorMap[status.toString()] || '#333333';
}

/**
 * 获取支付方式图标
 * @param {string} typename - 支付方式名称
 * @returns {string} 图标路径
 */
export function getPaymentIcon(typename) {
  if (!typename) return '/static/home/<USER>';

  const type = typename.toLowerCase();
  if (type.includes('wechat') || type.includes('微信') || type.includes('wxpay')) {
    return '/static/home/<USER>';
  } else if (type.includes('alipay') || type.includes('支付宝')) {
    return '/static/home/<USER>';
  } else if (type.includes('union') || type.includes('云闪付') || type.includes('银联')) {
    return '/static/home/<USER>';
  }
  return '/static/home/<USER>';
}

/**
 * 获取支付方式样式类
 * @param {string} typename - 支付方式名称
 * @returns {string} 样式类名
 */
export function getPaymentClass(typename) {
  if (!typename) return 'default';

  const type = typename.toLowerCase();
  if (type.includes('wechat') || type.includes('微信') || type.includes('wxpay')) {
    return 'wechat';
  } else if (type.includes('alipay') || type.includes('支付宝')) {
    return 'alipay';
  } else if (type.includes('union') || type.includes('云闪付') || type.includes('银联')) {
    return 'cloud';
  }
  return 'default';
}

/**
 * 格式化金额显示
 * @param {string|number} amount - 金额
 * @param {string} status - 订单状态
 * @returns {string} 格式化后的金额文本
 */
export function formatAmount(amount, status) {
  const formattedAmount = parseFloat(amount || 0).toFixed(2);

  switch (status) {
    case '1': // 已支付
      return `+ ¥ ${formattedAmount}`;
    case '0': // 未支付
      return `¥ ${formattedAmount}`;
    case '2': // 已退款
      return `- ¥ ${formattedAmount}`;
    case '3': // 已冻结
      return `¥ ${formattedAmount}`;
    default:
      return `¥ ${formattedAmount}`;
  }
}

/**
 * 格式化时间显示
 * @param {string} datetime - 日期时间字符串
 * @returns {string} 格式化后的时间
 */
export function formatTime(datetime) {
  if (!datetime) return '';

  // 提取时间部分 HH:MM:SS
  const timePart = datetime.split(' ')[1];
  if (timePart) {
    return timePart;
  }

  return datetime;
}

/**
 * 格式化日期显示
 * @param {string} dateStr - 日期字符串 YYYY-MM-DD
 * @returns {string} 格式化后的日期
 */
export function formatDate(dateStr) {
  if (!dateStr) return '';

  const parts = dateStr.split('-');
  if (parts.length === 3) {
    return `${parts[1].replace(/^0/, '')}月${parts[2].replace(/^0/, '')}日`;
  }
  return dateStr;
}

/**
 * 获取日期范围
 * @param {string} filterType - 筛选类型 (today, yesterday, week, month, custom)
 * @param {Array} customRange - 自定义日期范围 [startDate, endDate]
 * @returns {Object} 日期范围对象 {starttime, endtime}
 */
export function getDateRange(filterType, customRange = []) {
  const today = new Date();
  const result = {};

  switch (filterType) {
    case 'today':
      result.starttime = formatDateString(today);
      result.endtime = formatDateString(today);
      break;
    case 'yesterday':
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);
      result.starttime = formatDateString(yesterday);
      result.endtime = formatDateString(yesterday);
      break;
    case 'week':
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - today.getDay());
      result.starttime = formatDateString(weekStart);
      result.endtime = formatDateString(today);
      break;
    case 'month':
      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
      result.starttime = formatDateString(monthStart);
      result.endtime = formatDateString(today);
      break;
    case 'custom':
      if (customRange[0] && customRange[1]) {
        result.starttime = customRange[0];
        result.endtime = customRange[1];
      }
      break;
  }

  return result;
}

/**
 * 格式化日期为字符串 YYYY-MM-DD
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDateString(date) {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}