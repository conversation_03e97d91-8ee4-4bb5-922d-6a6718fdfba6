/**
 * 美观二维码生成器 v3.0
 * 专门生成美观、标准的QR码
 */

class BeautifulQRCode {
    constructor() {
        this.size = 21; // Version 1 标准尺寸
        this.modules = [];
        this.version = 1;
    }

    // 生成美观二维码
    generate(text) {
        // 根据文本长度确定版本
        this.determineVersion(text);
        
        // 初始化矩阵
        this.modules = Array(this.size).fill().map(() => Array(this.size).fill(0));
        
        // 添加标准功能图案
        this.addFinderPatterns();
        this.addSeparators();
        this.addTimingPatterns();
        this.addDarkModule();
        this.addFormatInfo();
        
        // 添加对齐图案（Version 2+）
        if (this.version >= 2) {
            this.addAlignmentPatterns();
        }
        
        // 编码数据
        this.encodeData(text);
        
        // 应用最佳掩码
        this.applyBestMask();
        
        return this.modules;
    }

    // 确定QR码版本
    determineVersion(text) {
        const length = text.length;
        if (length <= 10) {
            this.version = 1;
            this.size = 21;
        } else if (length <= 16) {
            this.version = 2;
            this.size = 25;
        } else if (length <= 26) {
            this.version = 3;
            this.size = 29;
        } else {
            this.version = 4;
            this.size = 33;
        }
    }

    // 添加定位图案（三个角的标准方块）
    addFinderPatterns() {
        const positions = [
            [0, 0],                    // 左上
            [this.size - 7, 0],       // 右上
            [0, this.size - 7]        // 左下
        ];

        positions.forEach(([x, y]) => {
            // 外层7x7黑框
            for (let i = 0; i < 7; i++) {
                for (let j = 0; j < 7; j++) {
                    this.modules[y + i][x + j] = 1;
                }
            }
            
            // 内层5x5白框
            for (let i = 1; i < 6; i++) {
                for (let j = 1; j < 6; j++) {
                    this.modules[y + i][x + j] = 0;
                }
            }
            
            // 中心3x3黑块
            for (let i = 2; i < 5; i++) {
                for (let j = 2; j < 5; j++) {
                    this.modules[y + i][x + j] = 1;
                }
            }
        });
    }

    // 添加分隔符（定位图案周围的白边）
    addSeparators() {
        // 左上角分隔符
        for (let i = 0; i < 8; i++) {
            this.modules[7][i] = 0;  // 水平
            this.modules[i][7] = 0;  // 垂直
        }
        
        // 右上角分隔符
        for (let i = 0; i < 8; i++) {
            this.modules[7][this.size - 8 + i] = 0;  // 水平
            this.modules[i][this.size - 8] = 0;      // 垂直
        }
        
        // 左下角分隔符
        for (let i = 0; i < 8; i++) {
            this.modules[this.size - 8][i] = 0;      // 水平
            this.modules[this.size - 8 + i][7] = 0;  // 垂直
        }
    }

    // 添加时序图案（第6行和第6列的交替模式）
    addTimingPatterns() {
        for (let i = 8; i < this.size - 8; i++) {
            this.modules[6][i] = i % 2;     // 第6行
            this.modules[i][6] = i % 2;     // 第6列
        }
    }

    // 添加暗模块
    addDarkModule() {
        this.modules[4 * this.version + 9][8] = 1;
    }

    // 添加格式信息
    addFormatInfo() {
        // 简化的格式信息（错误纠正级别M + 掩码0）
        const formatBits = [1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0];
        
        // 在定位图案周围添加格式信息
        for (let i = 0; i < 15; i++) {
            const bit = formatBits[i];
            
            // 左上角区域
            if (i < 6) {
                this.modules[8][i] = bit;
            } else if (i < 8) {
                this.modules[8][i + 1] = bit;
            } else {
                this.modules[8][this.size - 15 + i] = bit;
            }
            
            // 右下角区域
            if (i < 8) {
                this.modules[this.size - 1 - i][8] = bit;
            } else {
                this.modules[14 - i][8] = bit;
            }
        }
    }

    // 添加对齐图案（Version 2+）
    addAlignmentPatterns() {
        if (this.version === 2) {
            this.addAlignmentPattern(16, 16);
        } else if (this.version === 3) {
            this.addAlignmentPattern(20, 20);
        } else if (this.version === 4) {
            this.addAlignmentPattern(24, 24);
        }
    }

    // 添加单个对齐图案
    addAlignmentPattern(x, y) {
        for (let i = -2; i <= 2; i++) {
            for (let j = -2; j <= 2; j++) {
                const px = x + j;
                const py = y + i;
                if (px >= 0 && px < this.size && py >= 0 && py < this.size) {
                    if (Math.abs(i) === 2 || Math.abs(j) === 2 || (i === 0 && j === 0)) {
                        this.modules[py][px] = 1;
                    } else {
                        this.modules[py][px] = 0;
                    }
                }
            }
        }
    }

    // 编码数据
    encodeData(text) {
        // 创建数据位流
        const dataBits = this.createDataBits(text);
        
        // 使用标准的之字形填充模式
        this.fillDataBits(dataBits);
    }

    // 创建数据位流
    createDataBits(text) {
        const bits = [];
        
        // 模式指示符（字节模式：0100）
        bits.push(0, 1, 0, 0);
        
        // 字符计数指示符
        const count = text.length;
        for (let i = 7; i >= 0; i--) {
            bits.push((count >> i) & 1);
        }
        
        // 数据
        for (let i = 0; i < text.length; i++) {
            const byte = text.charCodeAt(i);
            for (let j = 7; j >= 0; j--) {
                bits.push((byte >> j) & 1);
            }
        }
        
        // 终止符
        for (let i = 0; i < 4 && bits.length < this.getDataCapacity(); i++) {
            bits.push(0);
        }
        
        // 填充到8位边界
        while (bits.length % 8 !== 0 && bits.length < this.getDataCapacity()) {
            bits.push(0);
        }
        
        // 填充字节
        const padBytes = [0xEC, 0x11]; // 11101100, 00010001
        let padIndex = 0;
        while (bits.length < this.getDataCapacity()) {
            const padByte = padBytes[padIndex % 2];
            for (let i = 7; i >= 0; i--) {
                if (bits.length < this.getDataCapacity()) {
                    bits.push((padByte >> i) & 1);
                }
            }
            padIndex++;
        }
        
        return bits;
    }

    // 获取数据容量
    getDataCapacity() {
        const capacities = [152, 272, 440, 640]; // Version 1-4的数据容量
        return capacities[this.version - 1] || 152;
    }

    // 填充数据位
    fillDataBits(bits) {
        let bitIndex = 0;
        let up = true;
        
        for (let col = this.size - 1; col > 0; col -= 2) {
            if (col === 6) col--; // 跳过时序列
            
            for (let i = 0; i < this.size; i++) {
                for (let c = 0; c < 2; c++) {
                    const x = col - c;
                    const y = up ? this.size - 1 - i : i;
                    
                    if (!this.isFunctionModule(x, y)) {
                        if (bitIndex < bits.length) {
                            this.modules[y][x] = bits[bitIndex];
                            bitIndex++;
                        } else {
                            this.modules[y][x] = 0;
                        }
                    }
                }
            }
            up = !up;
        }
    }

    // 检查是否为功能模块
    isFunctionModule(x, y) {
        // 定位图案和分隔符
        if ((x < 9 && y < 9) || 
            (x >= this.size - 8 && y < 9) || 
            (x < 9 && y >= this.size - 8)) {
            return true;
        }
        
        // 时序图案
        if (x === 6 || y === 6) {
            return true;
        }
        
        // 格式信息
        if ((x === 8 && (y < 9 || y >= this.size - 8)) ||
            (y === 8 && (x < 9 || x >= this.size - 7))) {
            return true;
        }
        
        // 暗模块
        if (x === 8 && y === 4 * this.version + 9) {
            return true;
        }
        
        // 对齐图案
        if (this.version >= 2) {
            const alignPos = this.getAlignmentPositions();
            for (const [ax, ay] of alignPos) {
                if (Math.abs(x - ax) <= 2 && Math.abs(y - ay) <= 2) {
                    return true;
                }
            }
        }
        
        return false;
    }

    // 获取对齐图案位置
    getAlignmentPositions() {
        if (this.version === 2) return [[16, 16]];
        if (this.version === 3) return [[20, 20]];
        if (this.version === 4) return [[24, 24]];
        return [];
    }

    // 应用最佳掩码
    applyBestMask() {
        // 使用简单的棋盘掩码
        for (let y = 0; y < this.size; y++) {
            for (let x = 0; x < this.size; x++) {
                if (!this.isFunctionModule(x, y)) {
                    if ((x + y) % 2 === 0) {
                        this.modules[y][x] = 1 - this.modules[y][x];
                    }
                }
            }
        }
    }

    // 渲染到Canvas
    toCanvas(canvas, text, options = {}) {
        const modules = this.generate(text);
        
        const ctx = canvas.getContext('2d');
        const moduleSize = Math.floor((options.width || 280) / this.size);
        const canvasSize = moduleSize * this.size;
        
        canvas.width = canvasSize;
        canvas.height = canvasSize;
        
        // 清除画布
        ctx.fillStyle = options.color?.light || '#FFFFFF';
        ctx.fillRect(0, 0, canvasSize, canvasSize);
        
        // 绘制模块
        ctx.fillStyle = options.color?.dark || '#000000';
        for (let y = 0; y < this.size; y++) {
            for (let x = 0; x < this.size; x++) {
                if (modules[y][x]) {
                    ctx.fillRect(x * moduleSize, y * moduleSize, moduleSize, moduleSize);
                }
            }
        }
    }
}

// 全局QRCode对象，兼容原有API
window.QRCode = {
    toCanvas: function(canvas, text, options, callback) {
        try {
            const qr = new BeautifulQRCode();
            
            // 处理参数
            if (typeof options === 'function') {
                callback = options;
                options = {};
            }
            
            // 如果canvas是字符串ID，获取元素
            if (typeof canvas === 'string') {
                canvas = document.getElementById(canvas);
            }
            
            // 生成美观二维码
            qr.toCanvas(canvas, text, options);
            
            // 调用回调
            if (callback) {
                setTimeout(() => callback(null), 10);
            }
        } catch (error) {
            console.error('QRCode生成错误:', error);
            if (callback) {
                callback(error);
            } else {
                throw error;
            }
        }
    }
};
