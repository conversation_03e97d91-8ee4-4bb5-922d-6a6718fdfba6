// api/merchant.js - 商户相关API
import { get, post } from '@/utils/request';
import { signParams, generateNonceStr } from '@/utils/sign';
import config from '@/config/index';

/**
 * 获取商户信息 (新版API)
 * @returns {Promise} 请求结果
 */
export function getMerchantInfo() {
  const merchantId = uni.getStorageSync('user_uid') || uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('user_key') || uni.getStorageSync('merchantKey') || config.merchant.key;

  console.log('🔍 获取商户信息参数:', {
    merchantId,
    merchantKey: merchantKey ? '***' + merchantKey.slice(-3) : '未设置'
  });

  // 使用后端的query接口获取商户信息
  return get('/api.php', {
    act: 'query',
    pid: merchantId,
    key: merchantKey
  });
}

/**
 * 获取商户信息 (旧版API，保留兼容)
 * @returns {Promise} 请求结果
 */
export function getMerchantInfoLegacy() {
  const merchantId = uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('merchantKey') || config.merchant.key;
  
  return get('/api.php', {
    act: 'query',
    pid: merchantId,
    key: merchantKey
  });
}

/**
 * 获取结算记录
 * @param {Number} limit - 每页记录数
 * @param {Number} offset - 偏移量
 * @returns {Promise} 请求结果
 */
export function getSettlements(limit = 10, offset = 0) {
  const merchantId = uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('merchantKey') || config.merchant.key;
  
  return get('/api.php', {
    act: 'settle',
    pid: merchantId,
    key: merchantKey,
    limit: limit,
    offset: offset
  });
}

/**
 * 获取商户统计数据
 * @returns {Promise} 请求结果
 */
export function getMerchantStats() {
  // 这个接口使用query接口的返回数据，包含了基础统计信息
  return getMerchantInfo();
}

/**
 * 保存商户配置
 * @param {String} merchantId - 商户ID
 * @param {String} merchantKey - 商户密钥
 */
export function saveMerchantConfig(merchantId, merchantKey) {
  uni.setStorageSync('merchantId', merchantId);
  uni.setStorageSync('merchantKey', merchantKey);
  
  // 返回成功结果
  return Promise.resolve({
    code: 0,
    message: '保存成功'
  });
}

/**
 * 获取商户配置
 * @returns {Object} 商户配置
 */
export function getMerchantConfig() {
  return {
    merchantId: uni.getStorageSync('merchantId') || config.merchant.id,
    merchantKey: uni.getStorageSync('merchantKey') || config.merchant.key
  };
} 