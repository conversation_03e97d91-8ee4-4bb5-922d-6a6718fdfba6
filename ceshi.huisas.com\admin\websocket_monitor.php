<?php
/**
 * WebSocket服务监控页面
 * 管理后台 - WebSocket服务状态监控
 */

include("../includes/common.php");
$title='WebSocket服务监控';
include './head.php';

// 引入WebSocket通知模块
require_once '../includes/websocket_notify_workerman.php';

// 处理AJAX请求
if(isset($_GET['act'])){
    switch($_GET['act']){
        case 'status':
            // 获取服务状态
            $notifier = new WebSocketNotifyWorkerman();
            $isRunning = $notifier->checkServiceStatus();
            $stats = $notifier->getServiceStats();
            
            $result = [
                'status' => $isRunning ? 'running' : 'stopped',
                'stats' => $stats,
                'timestamp' => time()
            ];
            
            header('Content-Type: application/json');
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            exit;
            
        case 'test':
            // 发送测试通知
            $notifier = new WebSocketNotifyWorkerman();
            $result = $notifier->sendTestNotification();
            
            header('Content-Type: application/json');
            echo json_encode(['success' => $result], JSON_UNESCAPED_UNICODE);
            exit;
            
        case 'restart':
            // 重启Swoole服务
            if(!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] != 1){
                echo json_encode(['success' => false, 'message' => '权限不足']);
                exit;
            }

            // 停止现有服务
            $stopCommand = 'cd /www/wwwroot/ceshi.huisas.com && php swoole_websocket_server.php stop > /dev/null 2>&1';
            exec($stopCommand);

            sleep(2); // 等待服务停止

            // 启动新服务
            $startCommand = 'cd /www/wwwroot/ceshi.huisas.com && php swoole_websocket_server.php start > /dev/null 2>&1 &';
            exec($startCommand);

            sleep(3); // 等待服务启动

            $notifier = new WebSocketNotifyWorkerman();
            $isRunning = $notifier->checkServiceStatus();

            header('Content-Type: application/json');
            echo json_encode(['success' => $isRunning], JSON_UNESCAPED_UNICODE);
            exit;

        case 'start':
            // 启动Swoole服务
            if(!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] != 1){
                echo json_encode(['success' => false, 'message' => '权限不足']);
                exit;
            }

            $command = 'cd /www/wwwroot/ceshi.huisas.com && php swoole_websocket_server.php start > /dev/null 2>&1 &';
            exec($command);

            sleep(3); // 等待服务启动

            $notifier = new WebSocketNotifyWorkerman();
            $isRunning = $notifier->checkServiceStatus();

            header('Content-Type: application/json');
            echo json_encode(['success' => $isRunning], JSON_UNESCAPED_UNICODE);
            exit;

        case 'stop':
            // 停止Swoole服务
            if(!isset($_SESSION['admin_login']) || $_SESSION['admin_login'] != 1){
                echo json_encode(['success' => false, 'message' => '权限不足']);
                exit;
            }

            $command = 'cd /www/wwwroot/ceshi.huisas.com && php swoole_websocket_server.php stop > /dev/null 2>&1';
            exec($command);

            sleep(2); // 等待服务停止

            header('Content-Type: application/json');
            echo json_encode(['success' => true], JSON_UNESCAPED_UNICODE);
            exit;
    }
}

// 获取初始状态
$notifier = new WebSocketNotifyWorkerman();
$isRunning = $notifier->checkServiceStatus();
$stats = $notifier->getServiceStats();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fa fa-wifi"></i> WebSocket服务监控
                        <span id="service-status" class="badge <?php echo $isRunning ? 'badge-success' : 'badge-danger'; ?>">
                            <?php echo $isRunning ? '运行中' : '已停止'; ?>
                        </span>
                    </h4>
                </div>
                <div class="card-body">
                    
                    <!-- 服务状态概览 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="current-connections"><?php echo $stats['current_connections'] ?? 0; ?></h4>
                                            <p class="mb-0">当前连接数</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="total-requests"><?php echo $stats['total_requests'] ?? 0; ?></h4>
                                            <p class="mb-0">总请求数</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-exchange fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="payment-notifications"><?php echo $stats['payment_notifications'] ?? 0; ?></h4>
                                            <p class="mb-0">支付通知数</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-bell fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="uptime"><?php echo $stats['uptime_formatted'] ?? '00:00:00'; ?></h4>
                                            <p class="mb-0">运行时间</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-clock-o fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 服务控制 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Swoole WebSocket 服务控制</h5>
                                </div>
                                <div class="card-body">
                                    <button type="button" class="btn btn-primary" onclick="refreshStatus()">
                                        <i class="fa fa-refresh"></i> 刷新状态
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="sendTestNotification()">
                                        <i class="fa fa-paper-plane"></i> 发送测试通知
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="runDiagnostics()">
                                        <i class="fa fa-stethoscope"></i> 运行诊断
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="startService()">
                                        <i class="fa fa-play"></i> 启动服务
                                    </button>
                                    <button type="button" class="btn btn-danger" onclick="stopService()">
                                        <i class="fa fa-stop"></i> 停止服务
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="restartService()">
                                        <i class="fa fa-refresh"></i> 重启服务
                                    </button>
                                    <a href="http://ceshi.huisas.com:8080/health" target="_blank" class="btn btn-info">
                                        <i class="fa fa-external-link"></i> 健康检查
                                    </a>
                                    <a href="http://ceshi.huisas.com:8080/stats" target="_blank" class="btn btn-secondary">
                                        <i class="fa fa-bar-chart"></i> 统计API
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 详细统计信息 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>服务信息</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <td><strong>WebSocket地址</strong></td>
                                            <td><code>ws://ceshi.huisas.com:8080</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>HTTP API地址</strong></td>
                                            <td><code>http://ceshi.huisas.com:8080</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>支付通知API</strong></td>
                                            <td><code>http://ceshi.huisas.com:8080/payment/notify</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>健康检查API</strong></td>
                                            <td><code>http://ceshi.huisas.com:8080/health</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>统计信息API</strong></td>
                                            <td><code>http://ceshi.huisas.com:8080/stats</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>框架</strong></td>
                                            <td><span class="badge badge-success">Swoole WebSocket</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>PHP版本</strong></td>
                                            <td><?php echo PHP_VERSION; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Swoole版本</strong></td>
                                            <td><?php echo extension_loaded('swoole') ? (function_exists('swoole_version') ? swoole_version() : '已安装') : '未安装'; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>内存使用</strong></td>
                                            <td id="memory-usage"><?php echo formatBytes($stats['memory_usage'] ?? 0); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>峰值内存</strong></td>
                                            <td id="memory-peak"><?php echo formatBytes($stats['memory_peak'] ?? 0); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>实时日志</h5>
                                </div>
                                <div class="card-body">
                                    <div id="log-container" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                                        <div class="text-muted">正在加载日志...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- WebSocket连接测试 -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>WebSocket连接测试</h5>
                                </div>
                                <div class="card-body">
                                    <button type="button" class="btn btn-primary" onclick="testWebSocketConnection()">
                                        <i class="fa fa-plug"></i> 测试WebSocket连接
                                    </button>
                                    <div id="websocket-test-result" class="mt-3"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 自动刷新状态
let autoRefreshInterval;

$(document).ready(function() {
    // 启动自动刷新
    autoRefreshInterval = setInterval(refreshStatus, 10000); // 每10秒刷新一次
    
    // 初始化日志显示
    loadRecentLogs();
});

// 刷新服务状态
function refreshStatus() {
    $.get('?act=status', function(data) {
        if (data.status === 'running') {
            $('#service-status').removeClass('badge-danger').addClass('badge-success').text('运行中');
        } else {
            $('#service-status').removeClass('badge-success').addClass('badge-danger').text('已停止');
        }
        
        if (data.stats) {
            $('#current-connections').text(data.stats.current_connections || 0);
            $('#total-requests').text(data.stats.total_messages || 0);
            $('#payment-notifications').text(data.stats.payment_notifications || 0);

            // 格式化运行时间
            const uptime = data.stats.uptime || 0;
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = uptime % 60;
            const uptimeFormatted = String(hours).padStart(2, '0') + ':' +
                                  String(minutes).padStart(2, '0') + ':' +
                                  String(seconds).padStart(2, '0');
            $('#uptime').text(uptimeFormatted);

            $('#memory-usage').text(formatBytes(data.stats.memory_usage || 0));
            $('#memory-peak').text(formatBytes(data.stats.memory_peak || 0));
        }
    }).fail(function() {
        $('#service-status').removeClass('badge-success').addClass('badge-danger').text('连接失败');
    });
}

// 发送测试通知
function sendTestNotification() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 发送中...';
    btn.disabled = true;
    
    $.get('?act=test', function(data) {
        if (data.success) {
            layer.msg('测试通知发送成功！', {icon: 1});
        } else {
            layer.msg('测试通知发送失败！', {icon: 2});
        }
    }).fail(function() {
        layer.msg('请求失败！', {icon: 2});
    }).always(function() {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 启动服务
function startService() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 启动中...';
    btn.disabled = true;

    $.get('?act=start', function(data) {
        if (data.success) {
            layer.msg('Swoole服务启动成功！', {icon: 1});
            setTimeout(refreshStatus, 2000);
        } else {
            layer.msg('Swoole服务启动失败！', {icon: 2});
        }
    }).fail(function() {
        layer.msg('启动请求失败！', {icon: 2});
    }).always(function() {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 停止服务
function stopService() {
    layer.confirm('确定要停止Swoole WebSocket服务吗？', {icon: 3, title: '确认停止'}, function(index) {
        layer.close(index);

        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 停止中...';
        btn.disabled = true;

        $.get('?act=stop', function(data) {
            if (data.success) {
                layer.msg('Swoole服务停止成功！', {icon: 1});
                setTimeout(refreshStatus, 1000);
            } else {
                layer.msg('Swoole服务停止失败！', {icon: 2});
            }
        }).fail(function() {
            layer.msg('停止请求失败！', {icon: 2});
        }).always(function() {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    });
}

// 重启服务
function restartService() {
    layer.confirm('确定要重启Swoole WebSocket服务吗？', {icon: 3, title: '确认重启'}, function(index) {
        layer.close(index);

        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 重启中...';
        btn.disabled = true;

        $.get('?act=restart', function(data) {
            if (data.success) {
                layer.msg('Swoole服务重启成功！', {icon: 1});
                setTimeout(refreshStatus, 3000);
            } else {
                layer.msg('Swoole服务重启失败！', {icon: 2});
            }
        }).fail(function() {
            layer.msg('重启请求失败！', {icon: 2});
        }).always(function() {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    });
}

// 运行系统诊断
function runDiagnostics() {
    const resultDiv = $('#websocket-test-result');
    resultDiv.html('<div class="alert alert-info"><i class="fa fa-spinner fa-spin"></i> 正在运行Swoole WebSocket系统诊断...</div>');

    // 诊断项目列表
    const diagnostics = [
        { name: 'Swoole扩展检查', url: 'http://ceshi.huisas.com:8080/health' },
        { name: '服务状态检查', url: 'http://ceshi.huisas.com:8080/stats' },
        { name: '支付通知API', url: 'http://ceshi.huisas.com:8080/payment/notify', method: 'POST' }
    ];

    let results = [];
    let completed = 0;

    diagnostics.forEach(function(diagnostic, index) {
        const startTime = Date.now();

        $.ajax({
            url: diagnostic.url,
            method: diagnostic.method || 'GET',
            timeout: 5000,
            data: diagnostic.method === 'POST' ? JSON.stringify({
                merchant_id: 'test',
                order_id: 'DIAG_' + Date.now(),
                amount: '0.01',
                status: 'test'
            }) : null,
            contentType: diagnostic.method === 'POST' ? 'application/json' : undefined
        }).done(function(data) {
            const responseTime = Date.now() - startTime;
            results[index] = {
                name: diagnostic.name,
                status: 'success',
                message: `响应正常 (${responseTime}ms)`,
                data: data
            };
        }).fail(function(xhr) {
            results[index] = {
                name: diagnostic.name,
                status: 'error',
                message: `连接失败: ${xhr.status} ${xhr.statusText}`,
                data: null
            };
        }).always(function() {
            completed++;
            if (completed === diagnostics.length) {
                displayDiagnosticResults(results, resultDiv);
            }
        });
    });
}

// 显示诊断结果
function displayDiagnosticResults(results, resultDiv) {
    let html = '';
    let successCount = 0;

    results.forEach(function(result) {
        if (result.status === 'success') successCount++;

        const statusClass = result.status === 'success' ? 'success' : 'danger';
        const icon = result.status === 'success' ? 'check' : 'times';

        html += '<div class="alert alert-' + statusClass + '">';
        html += '<i class="fa fa-' + icon + '"></i> <strong>' + result.name + '</strong>: ' + result.message;
        if (result.data && typeof result.data === 'object') {
            html += '<br><small><code>' + JSON.stringify(result.data).substring(0, 200) + '...</code></small>';
        }
        html += '</div>';
    });

    const successRate = Math.round((successCount / results.length) * 100);
    const overallClass = successRate >= 80 ? 'success' : (successRate >= 50 ? 'warning' : 'danger');

    html = '<div class="alert alert-' + overallClass + '">' +
           '<h5><i class="fa fa-stethoscope"></i> Swoole WebSocket 诊断结果 (' + successRate + '% 通过)</h5>' +
           '</div>' + html;

    // 添加建议
    if (successRate < 100) {
        html += '<div class="alert alert-info">';
        html += '<h6><i class="fa fa-lightbulb-o"></i> 建议:</h6>';
        html += '<ul>';
        if (successCount === 0) {
            html += '<li>检查Swoole WebSocket服务是否已启动</li>';
            html += '<li>确认端口8080未被其他程序占用</li>';
            html += '<li>检查防火墙设置</li>';
        } else {
            html += '<li>部分服务异常，建议重启Swoole WebSocket服务</li>';
            html += '<li>检查服务器资源使用情况</li>';
        }
        html += '</ul>';
        html += '</div>';
    }

    resultDiv.html(html);
}

// 测试Swoole WebSocket连接
function testWebSocketConnection() {
    const resultDiv = $('#websocket-test-result');
    resultDiv.html('<div class="alert alert-info"><i class="fa fa-spinner fa-spin"></i> 正在测试Swoole WebSocket连接...</div>');

    try {
        const ws = new WebSocket('ws://ceshi.huisas.com:8080');
        let connected = false;
        let timeout;
        let messageCount = 0;

        // 设置连接超时
        timeout = setTimeout(function() {
            if (!connected) {
                ws.close();
                resultDiv.html('<div class="alert alert-danger"><i class="fa fa-times"></i> Swoole WebSocket连接超时！请检查服务是否正常运行。</div>');
            }
        }, 10000);

        ws.onopen = function() {
            connected = true;
            clearTimeout(timeout);
            resultDiv.html('<div class="alert alert-success"><i class="fa fa-check"></i> Swoole WebSocket连接成功！</div>');

            // 发送认证消息
            const authMessage = {
                type: 'auth',
                data: {
                    merchant_id: 'test_admin',
                    staff_id: 'admin_test',
                    token: 'test_token_' + Date.now()
                }
            };

            ws.send(JSON.stringify(authMessage));
            resultDiv.append('<div class="alert alert-info"><i class="fa fa-arrow-right"></i> 已发送认证消息</div>');

            // 3秒后订阅测试频道
            setTimeout(function() {
                if (ws.readyState === WebSocket.OPEN) {
                    const subscribeMessage = {
                        type: 'subscribe',
                        data: {
                            channel: 'merchant_test_admin_payment'
                        }
                    };
                    ws.send(JSON.stringify(subscribeMessage));
                    resultDiv.append('<div class="alert alert-info"><i class="fa fa-arrow-right"></i> 已订阅测试频道</div>');
                }
            }, 3000);

            // 5秒后发送心跳
            setTimeout(function() {
                if (ws.readyState === WebSocket.OPEN) {
                    const pingMessage = {
                        type: 'ping',
                        data: {
                            timestamp: Date.now()
                        }
                    };
                    ws.send(JSON.stringify(pingMessage));
                    resultDiv.append('<div class="alert alert-info"><i class="fa fa-arrow-right"></i> 已发送心跳消息</div>');
                }
            }, 5000);
        };

        ws.onmessage = function(event) {
            messageCount++;
            try {
                const data = JSON.parse(event.data);
                let messageType = data.type || '未知类型';
                let messageContent = '';

                if (data.type === 'welcome') {
                    messageContent = '服务器欢迎消息';
                } else if (data.type === 'auth_result') {
                    messageContent = data.data.success ? '认证成功' : '认证失败';
                } else if (data.type === 'pong') {
                    messageContent = '心跳响应';
                } else {
                    messageContent = JSON.stringify(data).substring(0, 100);
                }

                resultDiv.append('<div class="alert alert-success"><i class="fa fa-arrow-left"></i> <strong>' + messageType + '</strong>: ' + messageContent + '</div>');
            } catch (e) {
                resultDiv.append('<div class="alert alert-info"><i class="fa fa-arrow-left"></i> 收到原始消息: ' + event.data.substring(0, 100) + '</div>');
            }
        };

        ws.onerror = function(error) {
            clearTimeout(timeout);
            resultDiv.html('<div class="alert alert-danger"><i class="fa fa-times"></i> Swoole WebSocket连接错误，请运行诊断检查详细问题</div>');
        };

        ws.onclose = function(event) {
            clearTimeout(timeout);
            if (connected) {
                resultDiv.append('<div class="alert alert-warning"><i class="fa fa-info-circle"></i> WebSocket连接已关闭 (代码: ' + event.code + ', 收到消息: ' + messageCount + ' 条)</div>');
            } else {
                resultDiv.html('<div class="alert alert-danger"><i class="fa fa-times"></i> Swoole WebSocket连接失败！请点击"运行诊断"查看详细问题。</div>');
            }
        };

        // 15秒后自动关闭连接
        setTimeout(function() {
            if (ws.readyState === WebSocket.OPEN) {
                resultDiv.append('<div class="alert alert-info"><i class="fa fa-clock-o"></i> 测试完成，正在关闭连接...</div>');
                ws.close();
            }
        }, 15000);

    } catch (error) {
        resultDiv.html('<div class="alert alert-danger"><i class="fa fa-times"></i> Swoole WebSocket测试异常: ' + error.message + '</div>');
    }
}

// 加载最近的日志
function loadRecentLogs() {
    // 显示Swoole WebSocket相关日志
    const logs = [
        '[' + new Date().toLocaleString() + '] [Swoole-WebSocket] 服务器启动成功 (端口:8080)',
        '[' + new Date().toLocaleString() + '] [Swoole-HTTP] HTTP API服务器启动成功',
        '[' + new Date().toLocaleString() + '] [Swoole-WebSocket] 等待客户端连接...',
        '[' + new Date().toLocaleString() + '] [Swoole-WebSocket] 支持协议: WebSocket + HTTP',
        '[' + new Date().toLocaleString() + '] [Swoole-WebSocket] 商户隔离机制已启用'
    ];

    $('#log-container').html(logs.join('<br>'));

    // 尝试加载真实日志文件
    $.get('../logs/websocket_integration.log').done(function(data) {
        if (data) {
            const lines = data.split('\n').slice(-10); // 最后10行
            $('#log-container').html(lines.join('<br>'));
        }
    }).fail(function() {
        // 如果无法加载日志文件，保持示例日志
    });
}

// 格式化字节数
function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 页面卸载时清理定时器
$(window).on('beforeunload', function() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
});
</script>

<?php
function formatBytes($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $unitIndex = 0;
    
    while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
        $bytes /= 1024;
        $unitIndex++;
    }
    
    return round($bytes, 2) . ' ' . $units[$unitIndex];
}

include './foot.php';
?>
