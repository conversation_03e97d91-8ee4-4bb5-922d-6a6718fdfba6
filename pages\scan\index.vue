<template>
  <view class="container">
    <!-- 优雅现代导航栏 -->
    <view class="elegant-navbar">
      <!-- 状态栏占位 -->
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

      <!-- 导航栏主体 -->
      <view class="navbar-main">
        <!-- 左侧：标题区域 -->
        <view class="navbar-title-section">
          <text class="page-title">收款码</text>
          <text class="page-subtitle">扫码向我付款</text>
        </view>

        <!-- 右侧：操作按钮 -->
        <view class="navbar-actions">
          <!-- WebSocket状态指示器 -->
          <view class="websocket-status-btn" @click="onWebSocketStatus">
            <view class="status-icon-wrapper" :class="{
              'connected': websocketStatus.connected && getVoiceSettings().enabled,
              'connecting': websocketStatus.connecting,
              'muted': websocketStatus.connected && !getVoiceSettings().enabled
            }">
              <text class="status-icon">{{ getVoiceSettings().enabled ? '🎵' : '🔇' }}</text>
            </view>
          </view>

          <!-- 分享按钮 -->
          <view class="action-btn" @click="shareCode">
            <view class="action-icon-wrapper">
              <text class="action-icon">📤</text>
            </view>
            <text class="action-text">分享</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 导航栏占位 - 自适应安全区域 -->
    <view class="navbar-placeholder"></view>

    <!-- 收款码内容 -->
    <view class="content">
      <!-- 商家信息卡片 -->
      <view class="qrcode-card">
        <view class="merchant-name">{{ merchantName }}</view>
        <view class="merchant-desc">扫码向我付款</view>
        
        <!-- 二维码 -->
        <view class="qrcode-container">
          <view class="qrcode" @click="showStyledQRCode">
            <view class="qrcode-placeholder" v-if="isGeneratingQR">
              <view class="loading-text">生成中...</view>
            </view>
            <view class="qrcode-placeholder" v-else>
              <image
                :src="qrCodeImage || '/static/code/qrcode-sample.png'"
                class="qrcode-image"
                mode="aspectFit"
                @error="onQRImageError"
                @load="onQRImageLoad"
              ></image>
              <view class="qr-click-hint">
                <text>点击查看样式化二维码</text>
              </view>
            </view>
          </view>
        </view>
      </view>


    </view>
    
    <!-- 收款码设置区域 -->
    <view class="settings-card">
      <view class="settings-title">收款码设置</view>
      
      <!-- 设置选项列表 -->
      <view class="settings-list">
        <!-- 收款码名称 -->
        <view class="setting-item" @click="showEditNameModal">
        <view class="setting-left">
            <text class="setting-icon tag-icon">🏷️</text>
          <text class="setting-label">收款码名称</text>
        </view>
        <view class="setting-right">
            <text class="setting-value">{{ paymentCodeName }}</text>
            <text class="arrow">></text>
        </view>
      </view>
      
        <!-- 绑定员工 -->
        <view class="setting-item" @click="showStaffModal">
        <view class="setting-left">
            <text class="setting-icon person-icon">👤</text>
          <text class="setting-label">绑定员工</text>
        </view>
        <view class="setting-right">
            <text class="setting-value">{{ selectedStaff?.name || '未绑定' }}</text>
            <text class="arrow">></text>
          </view>
        </view>

        <!-- 二维码样式 -->
        <view class="setting-item" @click="openStyleModal">
        <view class="setting-left">
            <text class="setting-icon style-icon">🎨</text>
          <text class="setting-label">二维码样式</text>
        </view>
        <view class="setting-right">
            <text class="setting-value">{{ getStyleLabel(selectedStyle) }}</text>
            <text class="arrow">></text>
          </view>
        </view>
      </view>
      
      <!-- 保存按钮 -->
      <view class="save-button">
        <text class="save-icon">💾</text>
        <text>保存</text>
      </view>
    </view>
    
    <!-- 已生成收款码区域 -->
    <view class="settings-card generated-codes-card">
      <view class="codes-header">
        <text>已生成收款码</text>
        <view class="code-actions-container">
          <text class="bind-code-btn" @click="showBindEmptyCodeModal">绑定空码</text>
          <text class="create-code-btn" @click="createNewCode">+ 创建收款码</text>
        </view>
      </view>
      
      <view class="code-list">
        <view 
          class="code-item"
          v-for="(code, index) in generatedCodes"
          :key="index"
          :class="'bg-' + code.color"
        >
        <view class="code-item-left">
            <view class="qr-icon-box">
              <uni-icons type="qrcode" size="20" color="#5145F7"></uni-icons>
          </view>
          <view class="code-info">
            <text class="code-name">{{ code.name }}</text>
              <text class="code-date">{{ code.position }} | {{ code.date }}</text>
          </view>
        </view>
        <view class="code-actions">
            <view class="action-btn view-btn" @click="viewCode(code)" title="查看收款码">
              <uni-icons type="eye" size="18" color="#5145F7"></uni-icons>
          </view>
            <view class="action-btn edit-btn" @click="editCode(code)" title="编辑">
              <uni-icons type="compose" size="18" color="#666666"></uni-icons>
          </view>
            <view class="action-btn delete-btn" @click="deleteCode(code)" title="删除">
              <uni-icons type="trash" size="18" color="#666666"></uni-icons>
            </view>
          </view>
        </view>
        
        <!-- 空码快捷入口 -->
        <view class="empty-code-entry" @click="showBindEmptyCodeModal">
          <view class="empty-code-icon">
            <uni-icons type="scan" size="24" color="#FFFFFF"></uni-icons>
          </view>
          <view class="empty-code-text">
            <text>扫描并绑定空码</text>
            <text class="empty-code-desc">快速绑定预制收款码</text>
          </view>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
          </view>
        </view>
      </view>
    </view>
    
  <!-- 修改名称弹窗 -->
  <view class="modal" v-if="showNameModal">
    <view class="modal-mask" @click="cancelEditName"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>修改收款码名称</text>
        <text class="close-icon" @click="cancelEditName">×</text>
      </view>
      <view class="modal-body">
        <input 
          class="name-input" 
          v-model="tempPaymentCodeName" 
          placeholder="请输入收款码名称"
          maxlength="20"
        />
      </view>
      <view class="modal-footer">
        <button class="cancel-btn" @click="cancelEditName">取消</button>
        <button class="confirm-btn" @click="confirmEditName">确定</button>
      </view>
          </view>
        </view>
        
  <!-- 选择员工弹窗 -->
  <view class="modal" v-if="showStaffListModal">
    <view class="modal-mask" @click="cancelSelectStaff"></view>
    <view class="modal-content staff-modal">
      <view class="modal-header">
        <text>选择员工</text>
        <text class="close-icon" @click="cancelSelectStaff">×</text>
      </view>
      <view class="modal-body staff-list">
          <view 
          class="staff-item" 
          v-for="(staff, index) in staffList" 
            :key="index"
          @click="selectStaff(staff)"
        >
          <view class="staff-item-left">
            <view class="staff-avatar" :class="'bg-' + staff.color">{{ staff.name.charAt(0) }}</view>
            <view class="staff-detail">
              <text class="staff-name">{{ staff.name }}</text>
              <text class="staff-role">{{ staff.role }}</text>
              </view>
            </view>
          <view class="staff-checkbox" :class="{ checked: tempSelectedStaffId === staff.id }">
            </view>
              </view>
      </view>
      <view class="modal-footer">
        <button class="cancel-btn" @click="cancelSelectStaff">取消</button>
        <button class="confirm-btn" @click="confirmSelectStaff">确定</button>
            </view>
          </view>
        </view>

  <!-- 选择二维码样式弹窗 -->
  <view class="modal" v-if="showStyleModal">
    <view class="modal-mask" @click="cancelSelectStyle"></view>
    <view class="modal-content style-modal">
      <view class="modal-header">
        <text>选择二维码样式</text>
        <text class="close-icon" @click="cancelSelectStyle">×</text>
      </view>
      <view class="modal-body style-list">
          <view
          class="style-item"
          v-for="(style, index) in qrCodeStyles"
            :key="index"
          @click="selectStyle(style.value)"
        >
          <view class="style-item-left">
            <view class="style-preview" :class="'style-' + style.value">
              <text class="style-icon">🎨</text>
            </view>
            <view class="style-detail">
              <text class="style-name">{{ style.label }}</text>
              </view>
            </view>
          <view class="style-checkbox" :class="{ checked: tempSelectedStyle === style.value }">
            </view>
              </view>
      </view>
      <view class="modal-footer">
        <button class="cancel-btn" @click="cancelSelectStyle">取消</button>
        <button class="confirm-btn" @click="confirmSelectStyle">确定</button>
            </view>
          </view>
        </view>

  <!-- 绑定空码弹窗 -->
  <view class="modal" v-if="showBindCodeModal">
    <view class="modal-mask" @click="cancelBindCode"></view>
    <view class="modal-content bind-code-modal">
      <view class="modal-header">
        <text>绑定空码</text>
        <text class="close-icon" @click="cancelBindCode">×</text>
        </view>
      <view class="modal-body">
        <view class="bind-code-options">
          <view class="bind-option" @click="scanBindCode">
            <view class="bind-option-icon scan-icon">
              <uni-icons type="scan" size="28" color="#FFFFFF"></uni-icons>
      </view>
            <text class="bind-option-text">扫描空码</text>
            <text class="bind-option-desc">扫描预制码快速绑定</text>
          </view>
          <view class="bind-option" @click="enterCodeManually">
            <view class="bind-option-icon manual-icon">
              <uni-icons type="compose" size="28" color="#FFFFFF"></uni-icons>
            </view>
            <text class="bind-option-text">手动输入</text>
            <text class="bind-option-desc">输入空码编号进行绑定</text>
          </view>
        </view>
        
        <view class="code-manual-input" v-if="showManualInput">
          <input 
            class="code-input" 
            v-model="manualCodeInput" 
            placeholder="请输入空码编号"
            maxlength="20"
          />
          <button class="verify-btn" @click="verifyCode">验证</button>
        </view>
        
        <view class="code-tips">
          <text class="tips-title">空码说明：</text>
          <text class="tips-content">空码是指未绑定商家信息的收款码，通过绑定空码可快速生成新的收款码，无需等待制作。</text>
        </view>
        </view>
      <view class="modal-footer" v-if="!showManualInput">
        <button class="cancel-btn" @click="cancelBindCode">取消</button>
        <button class="confirm-btn" @click="scanBindCode">开始扫码</button>
      </view>
    </view>
  </view>

  <!-- 创建收款码弹窗 -->
  <view class="modal" v-if="showCreateModal">
    <view class="modal-mask" @click="cancelCreate"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>创建收款码</text>
        <text class="close-icon" @click="cancelCreate">×</text>
      </view>
      <view class="modal-body">
        <view class="form-item">
          <text class="form-label">收款码名称</text>
          <input
            class="form-input"
            v-model="createForm.name"
            placeholder="请输入收款码名称"
            maxlength="50"
          />
        </view>
        <view class="form-item">
          <text class="form-label">绑定员工</text>
          <view class="form-select" @click="showCreateStaffPicker">
            <text class="select-text">{{ getSelectedStaffName(createForm.staff_id) }}</text>
            <text class="arrow">▼</text>
          </view>
        </view>
        <view class="form-item">
          <text class="form-label">收款码样式</text>
          <view class="form-select" @click="showCreateStylePicker">
            <text class="select-text">{{ getStyleLabel(createForm.style) }}</text>
            <text class="arrow">▼</text>
          </view>
        </view>
        <view class="form-item">
          <text class="form-label">描述信息</text>
          <textarea
            class="form-textarea"
            v-model="createForm.description"
            placeholder="请输入描述信息（可选）"
            maxlength="200"
          ></textarea>
        </view>
      </view>
      <view class="modal-footer">
        <button class="cancel-btn" @click="cancelCreate">取消</button>
        <button class="confirm-btn" @click="saveCreateQRCode">创建</button>
      </view>
    </view>
  </view>

  <!-- 编辑收款码弹窗 -->
  <view class="modal" v-if="showEditModal">
    <view class="modal-mask" @click="cancelEdit"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>编辑收款码</text>
        <text class="close-icon" @click="cancelEdit">×</text>
      </view>
      <view class="modal-body">
        <view class="form-item">
          <text class="form-label">收款码名称</text>
          <input
            class="form-input"
            v-model="editForm.name"
            placeholder="请输入收款码名称"
            maxlength="50"
          />
        </view>
        <view class="form-item">
          <text class="form-label">绑定员工</text>
          <view class="form-select" @click="showEditStaffPicker">
            <text class="select-text">{{ getSelectedStaffName(editForm.staff_id) }}</text>
            <text class="arrow">▼</text>
          </view>
        </view>
        <view class="form-item">
          <text class="form-label">收款码样式</text>
          <view class="form-select" @click="showEditStylePicker">
            <text class="select-text">{{ getStyleLabel(editForm.style) }}</text>
            <text class="arrow">▼</text>
          </view>
        </view>
        <view class="form-item">
          <text class="form-label">描述信息</text>
          <textarea
            class="form-textarea"
            v-model="editForm.description"
            placeholder="请输入描述信息（可选）"
            maxlength="200"
          ></textarea>
        </view>
      </view>
      <view class="modal-footer">
        <button class="cancel-btn" @click="cancelEdit">取消</button>
        <button class="confirm-btn" @click="saveEditQRCode">保存</button>
      </view>
    </view>
  </view>

  <!-- 创建收款码 - 员工选择下拉 -->
  <view class="dropdown-overlay" v-if="showCreateStaffDropdown" @click="hideCreateStaffPicker">
    <view class="dropdown-content" @click.stop>
      <view class="dropdown-header">
        <text>选择绑定员工</text>
      </view>
      <view class="dropdown-list">
        <view
          class="dropdown-item"
          :class="{ active: createForm.staff_id === null }"
          @click="selectCreateStaff(null)"
        >
          <text>商户自己</text>
          <text v-if="createForm.staff_id === null" class="check-icon">✓</text>
        </view>
        <view
          v-for="staff in staffList"
          :key="staff.id"
          class="dropdown-item"
          :class="{ active: createForm.staff_id === staff.id }"
          @click="selectCreateStaff(staff.id)"
        >
          <text>{{ staff.name }} ({{ staff.role }})</text>
          <text v-if="createForm.staff_id === staff.id" class="check-icon">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 创建收款码 - 样式选择下拉 -->
  <view class="dropdown-overlay" v-if="showCreateStyleDropdown" @click="hideCreateStylePicker">
    <view class="dropdown-content" @click.stop>
      <view class="dropdown-header">
        <text>选择收款码样式</text>
      </view>
      <view class="dropdown-list">
        <view
          v-for="style in qrCodeStyles"
          :key="style.value"
          class="dropdown-item"
          :class="{ active: createForm.style === style.value }"
          @click="selectCreateStyle(style.value)"
        >
          <view class="dropdown-item-content">
            <text>{{ style.label }}</text>
          </view>
          <text v-if="createForm.style === style.value" class="check-icon">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 编辑收款码 - 员工选择下拉 -->
  <view class="dropdown-overlay" v-if="showEditStaffDropdown" @click="hideEditStaffPicker">
    <view class="dropdown-content" @click.stop>
      <view class="dropdown-header">
        <text>选择绑定员工</text>
      </view>
      <view class="dropdown-list">
        <view
          class="dropdown-item"
          :class="{ active: editForm.staff_id === null }"
          @click="selectEditStaff(null)"
        >
          <text>商户自己</text>
          <text v-if="editForm.staff_id === null" class="check-icon">✓</text>
        </view>
        <view
          v-for="staff in staffList"
          :key="staff.id"
          class="dropdown-item"
          :class="{ active: editForm.staff_id === staff.id }"
          @click="selectEditStaff(staff.id)"
        >
          <text>{{ staff.name }} ({{ staff.role }})</text>
          <text v-if="editForm.staff_id === staff.id" class="check-icon">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 编辑收款码 - 样式选择下拉 -->
  <view class="dropdown-overlay" v-if="showEditStyleDropdown" @click="hideEditStylePicker">
    <view class="dropdown-content" @click.stop>
      <view class="dropdown-header">
        <text>选择收款码样式</text>
      </view>
      <view class="dropdown-list">
        <view
          v-for="style in qrCodeStyles"
          :key="style.value"
          class="dropdown-item"
          :class="{ active: editForm.style === style.value }"
          @click="selectEditStyle(style.value)"
        >
          <view class="dropdown-item-content">
            <text>{{ style.label }}</text>
          </view>
          <text v-if="editForm.style === style.value" class="check-icon">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 预制码绑定弹窗 -->
  <view class="modal" v-if="showPreCodeBindModal">
    <view class="modal-mask" @click="cancelPreCodeBind"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text>绑定空码</text>
        <text class="close-icon" @click="cancelPreCodeBind">×</text>
      </view>
      <view class="modal-body">
        <view class="form-item">
          <text class="form-label">预制码</text>
          <input
            class="form-input readonly-input"
            v-model="preCodeBindForm.precode"
            placeholder="预制码"
            readonly
          />
        </view>
        <view class="form-item">
          <text class="form-label">收款码名称</text>
          <input
            class="form-input"
            v-model="preCodeBindForm.name"
            placeholder="请输入收款码名称"
            maxlength="50"
          />
        </view>
        <view class="form-item">
          <text class="form-label">绑定员工</text>
          <view class="form-select" @click="showPreCodeStaffPicker">
            <text class="select-text">{{ getSelectedStaffName(preCodeBindForm.staff_id) }}</text>
            <text class="arrow">▼</text>
          </view>
        </view>
        <view class="form-item">
          <text class="form-label">收款码样式</text>
          <view class="form-select" @click="showPreCodeStylePicker">
            <text class="select-text">{{ getStyleLabel(preCodeBindForm.style) }}</text>
            <text class="arrow">▼</text>
          </view>
        </view>
        <view class="form-item">
          <text class="form-label">描述信息</text>
          <textarea
            class="form-textarea"
            v-model="preCodeBindForm.description"
            placeholder="请输入描述信息（可选）"
            maxlength="200"
          ></textarea>
        </view>
      </view>
      <view class="modal-footer">
        <button class="cancel-btn" @click="cancelPreCodeBind">取消</button>
        <button class="confirm-btn" @click="confirmPreCodeBind">绑定</button>
      </view>
    </view>
  </view>

  <!-- 预制码绑定 - 员工选择下拉 -->
  <view class="dropdown-overlay" v-if="showPreCodeStaffDropdown" @click="hidePreCodeStaffPicker">
    <view class="dropdown-content" @click.stop>
      <view class="dropdown-header">
        <text>选择绑定员工</text>
      </view>
      <view class="dropdown-list">
        <view
          class="dropdown-item"
          :class="{ active: preCodeBindForm.staff_id === null }"
          @click="selectPreCodeStaff(null)"
        >
          <text>商户自己</text>
          <text v-if="preCodeBindForm.staff_id === null" class="check-icon">✓</text>
        </view>
        <view
          v-for="staff in staffList"
          :key="staff.id"
          class="dropdown-item"
          :class="{ active: preCodeBindForm.staff_id === staff.id }"
          @click="selectPreCodeStaff(staff.id)"
        >
          <text>{{ staff.name }} ({{ staff.role }})</text>
          <text v-if="preCodeBindForm.staff_id === staff.id" class="check-icon">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 预制码绑定 - 样式选择下拉 -->
  <view class="dropdown-overlay" v-if="showPreCodeStyleDropdown" @click="hidePreCodeStylePicker">
    <view class="dropdown-content" @click.stop>
      <view class="dropdown-header">
        <text>选择收款码样式</text>
      </view>
      <view class="dropdown-list">
        <view
          v-for="style in qrCodeStyles"
          :key="style.value"
          class="dropdown-item"
          :class="{ active: preCodeBindForm.style === style.value }"
          @click="selectPreCodeStyle(style.value)"
        >
          <view class="dropdown-item-content">
            <text>{{ style.label }}</text>
          </view>
          <text v-if="preCodeBindForm.style === style.value" class="check-icon">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 🎨 样式化二维码弹窗 -->
  <view class="modal styled-qr-modal" v-if="showStyledQRModal">
    <view class="modal-mask" @click="closeStyledQRModal"></view>
    <view class="modal-content styled-qr-content">
      <view class="modal-header">
        <text>{{ paymentCodeName }}</text>
        <text class="close-icon" @click="closeStyledQRModal">×</text>
      </view>
      <view class="modal-body styled-qr-body">
        <!-- 样式化二维码显示 -->
        <view class="styled-qr-container">
          <view class="styled-qr-wrapper" v-if="isGeneratingStyledQR">
            <view class="styled-qr-loading">
              <text>生成样式化二维码中...</text>
            </view>
          </view>
          <view class="styled-qr-wrapper" v-else>
            <image
              :src="styledQRCodeImage || '/static/code/qrcode-placeholder.png'"
              class="styled-qr-image"
              mode="aspectFit"
              @longpress="saveStyledQRCode"
            ></image>
          </view>
          <text class="styled-qr-tip">长按二维码保存图片</text>
        </view>

        <!-- 样式信息 -->
        <view class="style-info">
          <text class="style-name">当前样式：{{ getStyleLabel(selectedStyle) }}</text>
          <text class="style-desc">包含商家信息和装饰元素的完整收款码</text>
        </view>
      </view>
      <view class="modal-footer styled-qr-footer">
        <button class="save-styled-btn" @click="saveStyledQRCode">
          <text class="save-icon">💾</text>
          <text>保存到相册</text>
        </button>
        <button class="share-styled-btn" @click="shareStyledQRCode">
          <text class="share-icon">📤</text>
          <text>分享收款码</text>
        </button>
      </view>
    </view>
  </view>


  <!-- 🎯 查看收款码弹窗 -->
  <view class="modal view-qr-modal" v-if="showViewModal">
    <view class="modal-mask" @click="cancelViewCode"></view>
    <view class="modal-content view-qr-content">
      <view class="modal-header">
        <text>{{ viewCodeData.name || '收款码' }}</text>
        <text class="close-icon" @click="cancelViewCode">×</text>
      </view>
      <view class="modal-body view-qr-body">

        <!-- 样式化二维码显示区域 -->
        <view class="view-qr-container">
          <view class="view-qr-code">
            <view class="qr-loading" v-if="isLoadingViewQR">
              <text>生成样式化收款码中...</text>
            </view>
            <!-- 小程序环境：样式化二维码显示 -->
            <!-- #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ -->
            <view v-else-if="viewBackgroundImage && viewStyleConfig" class="styled-qr-container">
              <!-- 背景图片 -->
              <image
                :src="viewBackgroundImage"
                class="styled-qr-background"
                mode="aspectFit"
              ></image>
              <!-- 二维码覆盖层 -->
              <image
                :src="viewQRCodeImage"
                class="styled-qr-overlay"
                mode="aspectFit"
                :style="getQROverlayStyle(viewStyleConfig)"
                @longpress="saveViewQRCode"
              ></image>
            </view>
            <!-- #endif -->
            <!-- H5和APP环境：直接显示合成后的图片 -->
            <!-- #ifdef H5 || APP-PLUS -->
            <image
              v-else
              :src="viewQRCodeImage || '/static/code/qrcode-placeholder.png'"
              class="view-qr-image"
              mode="aspectFit"
              @longpress="saveViewQRCode"
            ></image>
            <!-- #endif -->
            <!-- 小程序环境：简单二维码显示 -->
            <!-- #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ -->
            <image
              v-else
              :src="viewQRCodeImage || '/static/code/qrcode-placeholder.png'"
              class="view-qr-image"
              mode="aspectFit"
              @longpress="saveViewQRCode"
            ></image>
            <!-- #endif -->
          </view>
          <text class="qr-tip">长按二维码保存图片</text>
        </view>
      </view>
      <view class="modal-footer view-qr-footer">
        <button class="save-qr-btn" @click="saveViewQRCode">
          <text class="save-icon">💾</text>
          <text>保存图片</text>
        </button>
      </view>
    </view>

  <!-- 隐藏的Canvas用于小程序样式化二维码生成 -->
  <!-- #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ -->
  <canvas
    id="styled-canvas"
    type="2d"
    style="position: fixed; top: -9999px; left: -9999px; width: 1px; height: 1px; opacity: 0;"
  ></canvas>
  <!-- #endif -->

  </view>
</template>

<script>
import CustomNavbar from '@/components/custom-navbar.vue'
import pageAuthMixin from '@/mixins/page-auth'
import { websocketMixin } from '@/mixins/websocketMixin.js'
import voiceManager from '@/utils/voiceManager.js'
import { request } from '@/utils/request'
import { generateQRCode } from '@/utils/qrcode'
import { getStaffList, saveQRConfig, editQRConfig, deleteQRConfig, getQRConfigListNew, createQRConfig, bindPreCode, verifyPreCode } from '@/api/staff'

export default {
  components: {
    CustomNavbar
  },
  // 🔧 重新启用WebSocket Mixin，确保收款码页面也能语音播报
  mixins: [pageAuthMixin, websocketMixin],
  data() {
    return {
      // 系统信息
      statusBarHeight: 20,
      merchantName: '商家收款助手',
      paymentCodeName: '默认收款码',
      selectedStaff: null,
      tempSelectedStaffId: null,
      showNameModal: false,
      showStaffListModal: false,
      showStyleModal: false,
      showBindCodeModal: false,
      showManualInput: false,
      tempSelectedStyle: 'native',
      tempPaymentCodeName: '',
      manualCodeInput: '',
      // 二维码相关
      qrCodeUrl: '',
      qrCodeImage: '',
      qrCodeStyles: [],
      selectedStyle: 'native',
      isGeneratingQR: false,
      // 收款码配置
      qrConfig: null,
      staffList: [],
      generatedCodes: [],
      // 创建收款码相关
      showCreateModal: false,
      showEditModal: false,
      showPreCodeModal: false,
      showPreCodeBindModal: false,
      // 🎨 样式化二维码相关
      showStyledQRModal: false,
      styledQRCodeImage: '',
      isGeneratingStyledQR: false,
      // 🎯 查看收款码相关
      showViewModal: false,
      viewCodeData: {},
      viewQRCodeImage: '',
      viewBackgroundImage: '', // 小程序样式化二维码背景图片
      viewStyleConfig: null,   // 小程序样式化二维码配置
      isLoadingViewQR: false,
      // 下拉选择状态
      showCreateStaffDropdown: false,
      showCreateStyleDropdown: false,
      showEditStaffDropdown: false,
      showEditStyleDropdown: false,
      showPreCodeStaffDropdown: false,
      showPreCodeStyleDropdown: false,
      createForm: {
        name: '',
        style: 'default',
        staff_id: null,
        fixed_amount: '',
        description: '',
        precode: ''
      },
      editForm: {},
      preCodeForm: {
        precode: '',
        name: '',
        style: 'default',
        staff_id: null
      },
      // 预制码绑定表单
      preCodeBindForm: {
        precode: '',
        name: '',
        style: 'native',
        staff_id: null,
        description: ''
      },
      availablePreCodes: [],
      // WebSocket状态
      websocketStatus: {
        connected: false,
        connecting: false
      }
    }
  },

  async onLoad() {
    console.log('📱 收款码页面加载');

    // 初始化系统信息
    this.initSystemInfo();

    // 🔧 重新启用WebSocket状态更新
    this.updateWebSocketStatus();

    // 先检查基本的登录状态
    const userToken = uni.getStorageSync('user_token');
    const userUid = uni.getStorageSync('user_uid');

    console.log('🔑 检查本地登录信息:', {
      hasToken: !!userToken,
      tokenLength: userToken ? userToken.length : 0,
      uid: userUid
    });

    if (!userToken || !userUid) {
      console.log('❌ 未找到登录信息，跳转到登录页');
      uni.showToast({
        title: '请先登录',
        icon: 'none'
      });
      uni.navigateTo({
        url: '/pages/login/index'
      });
      return;
    }

    await this.initPageWithAuth(this.loadScanPageData);
  },

  onShow() {
    console.log('📱 收款码页面显示');
    this.checkLoginOnShow();
    // 🔧 重新启用WebSocket状态更新
    this.updateWebSocketStatus();
  },

  onHide() {
    console.log('📱 收款码页面隐藏');
  },

  onUnload() {
    console.log('📱 收款码页面卸载');
    // 全局WebSocket服务由App.vue管理，这里不需要断开
  },

  methods: {


    // 初始化系统信息
    initSystemInfo() {
      const systemInfo = uni.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight || 20;

      // 特殊处理iPhone 14 Pro Max等设备
      const model = systemInfo.model || '';
      const platform = systemInfo.platform || '';

      console.log('设备信息:', {
        model,
        platform,
        statusBarHeight: this.statusBarHeight,
        safeAreaInsets: systemInfo.safeAreaInsets
      });

      // iPhone 14 Pro Max的状态栏通常是47px
      if (model.includes('iPhone') && this.statusBarHeight > 40) {
        console.log('检测到iPhone Pro Max系列设备，状态栏高度:', this.statusBarHeight);
      }

      // 设置CSS变量，供样式使用
      // #ifdef H5
      if (typeof document !== 'undefined') {
        const app = document.documentElement || document.body;
        if (app && app.style) {
          app.style.setProperty('--status-bar-height', this.statusBarHeight + 'px');
        }
      }
      // #endif
    },

    // 加载收款码页面特定数据
    async loadScanPageData() {
      try {
        console.log('📊 加载收款码页面数据');

        // 更新商家名称和收款码名称
        if (this.userInfo && this.userInfo.username) {
          this.merchantName = this.userInfo.username;
        }

        // 获取用户的收款码名称
        if (this.userInfo && this.userInfo.codename) {
          this.paymentCodeName = this.userInfo.codename;
        } else if (this.userInfo && this.userInfo.username) {
          this.paymentCodeName = this.userInfo.username;
        }

        // 生成收款码URL（关键步骤，必须成功）
        try {
          await this.generateQRCodeUrl();
        } catch (error) {
          console.error('❌ 生成收款码URL失败:', error);
          uni.showToast({
            title: '生成收款码失败',
            icon: 'none'
          });
          return;
        }

        // 加载员工列表（非关键步骤）
        try {
          await this.loadStaffList();
        } catch (error) {
          console.error('⚠️ 加载员工列表失败:', error);
        }

        // 加载二维码样式配置（非关键步骤）
        try {
          await this.loadQRCodeConfig();
        } catch (error) {
          console.error('⚠️ 加载二维码样式配置失败:', error);
        }

        // 加载用户偏好的样式
        try {
          const savedStyle = uni.getStorageSync('qr_style_preference');
          if (savedStyle && this.qrCodeStyles.find(s => s.value === savedStyle)) {
            this.selectedStyle = savedStyle;
          }
        } catch (error) {
          console.error('⚠️ 加载用户偏好样式失败:', error);
        }

        // 加载收款码列表（非关键步骤）
        try {
          await this.loadQRCodeList();
        } catch (error) {
          console.error('⚠️ 加载收款码列表失败:', error);
        }

        // 生成默认二维码（关键步骤）
        try {
          await this.generateQRCode(this.selectedStyle);
          console.log('✅ 收款码页面数据加载完成');
        } catch (error) {
          console.error('❌ 生成默认二维码失败:', error);
          // 🔧 小程序环境下，即使二维码生成失败也不阻止页面加载
          console.log('⚠️ 二维码生成失败，但继续加载页面');
          // 设置一个占位图片
          this.qrCodeImage = '/static/code/qrcode-sample.png';
        }

      } catch (error) {
        console.error('❌ 加载收款码页面数据失败:', error);
        uni.showToast({
          title: '页面加载失败',
          icon: 'none'
        });
      }
    },
    // 生成收款码URL
    async generateQRCodeUrl() {
      try {
        // 调用后端API生成收款码URL
        const response = await request({
          url: '/user/staff.php?act=generateQRUrl',
          method: 'POST',
          data: {
            staff_id: this.selectedStaff ? this.selectedStaff.id : 0
          }
        });

        console.log('📥 收款码URL响应:', response);

        if (response && response.code === 0) {
          this.qrCodeUrl = response.data.qr_url;
          console.log('✅ 收款码URL生成成功:', this.qrCodeUrl);
        } else {
          throw new Error(response?.msg || '生成收款码URL失败');
        }
      } catch (error) {
        console.error('❌ 生成收款码URL失败:', error);

        // 如果API调用失败，使用备用方案
        const uid = uni.getStorageSync('user_uid');
        if (uid) {
          // 使用简化的URL格式作为备用
          let qrUrl = `http://ceshi.huisas.com/paypage/?uid=${uid}`;
          if (this.selectedStaff && this.selectedStaff.id) {
            qrUrl += `&staff_id=${this.selectedStaff.id}`;
          }
          this.qrCodeUrl = qrUrl;
          console.log('⚠️ 使用备用收款码URL:', this.qrCodeUrl);
        } else {
          throw error;
        }
      }
    },

    // 加载二维码样式配置
    async loadQRCodeConfig() {
      try {
        console.log('📊 加载二维码样式配置...');

        // 从后端获取配置文件
        const response = await request({
          url: '/user/assets/js/config.json',
          method: 'GET',
          loading: false
        });

        this.qrConfig = response;

        // 生成样式选项列表 - 基于后端配置动态生成
        this.qrCodeStyles = [
          { value: 'native', label: 'H5原生' },
          { value: 'pikaqiu', label: '🎨 皮卡丘' },
          { value: 'kanuobudingmao', label: '🐱 布叮猫' },
          { value: 'niannianyouyu', label: '🐟 年年有余' },
          { value: 'xiaohuangren', label: '👤 小黄人' },
          { value: 'qitao', label: '🙏 乞讨' },
          { value: 'baobei', label: '💎 宝贝' },
          { value: 'toushi', label: '🍎 投食' },
          { value: 'gongzhu', label: '👸 公主' },
          { value: 'qiuzanzhu', label: '💰 求赞助' },
          { value: 'huanyingdashang', label: '🎁 欢迎打赏' },
          { value: 'yinlian', label: '💳 银联' },
          { value: 'yitiji', label: '🖥️ 一体机' },
          { value: 'maomi', label: '🐱 猫咪' },
          { value: 'longmao', label: '🐉 龙猫' }
        ];

        console.log('✅ 二维码样式配置加载成功');
      } catch (error) {
        console.error('❌ 加载二维码样式配置失败:', error);
        // 使用默认配置
        this.qrConfig = {
          native: {
            qrWidth: 550,
            qrHeight: 550,
            foreground: '#FFFFFF',
            background: '#000000',
            imgWidth: 900,
            imgHeight: 1200,
            font: "70px '黑体'",
            fontColor: '#000000',
            recNameLeft: '',
            recNameTop: 178,
            qrLeft: 145,
            qrTop: 350,
            url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTAwIiBoZWlnaHQ9IjEyMDAiIHZpZXdCb3g9IjAgMCA5MDAgMTIwMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjkwMCIgaGVpZ2h0PSIxMjAwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjQ1MCIgeTE9IjAiIHgyPSI0NTAiIHkyPSIxMjAwIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiM2NjY2RkYiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjOTk5OUZGIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+'
          },
          style1: {
            qrWidth: 400,
            qrHeight: 400,
            foreground: '#000000',
            background: '#FFFFFF',
            imgWidth: 800,
            imgHeight: 1000,
            font: "60px -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
            fontColor: '#FFFFFF',
            recNameLeft: '',
            recNameTop: 150,
            qrLeft: 200,
            qrTop: 300,
            url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjEwMDAiIHZpZXdCb3g9IjAgMCA4MDAgMTAwMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwMCIgaGVpZ2h0PSIxMDAwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjQwMCIgeTE9IjAiIHgyPSI0MDAiIHkyPSIxMDAwIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiMwMDdBRkYiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjMzRDNzU5Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+'
          }
        };

        // 设置默认样式选项列表
        this.qrCodeStyles = [
          { value: 'native', label: 'H5原生' },
          { value: 'pikaqiu', label: '🎨 皮卡丘' },
          { value: 'kanuobudingmao', label: '🐱 布叮猫' },
          { value: 'niannianyouyu', label: '🐟 年年有余' },
          { value: 'xiaohuangren', label: '👤 小黄人' }
        ];
      }
    },

    // 加载员工列表
    async loadStaffList() {
      try {
        console.log('👥 加载员工列表...');

        const response = await getStaffList();
        console.log('📥 员工列表响应:', response);

        if (response && response.code === 0) {
          this.staffList = response.data || [];
          console.log('✅ 员工列表加载成功:', this.staffList.length, '个员工');
        } else {
          console.warn('⚠️ 员工列表加载失败，使用默认数据');
          // 使用默认员工数据
          this.staffList = [
            { id: 1, name: '李店长', role: '店长', color: 'purple' },
            { id: 2, name: '王收银', role: '收银员', color: 'green' },
            { id: 3, name: '张收银', role: '收银员', color: 'blue' }
          ];
        }
      } catch (error) {
        console.error('❌ 加载员工列表失败:', error);
        // 使用默认员工数据
        this.staffList = [
          { id: 1, name: '李店长', role: '店长', color: 'purple' },
          { id: 2, name: '王收银', role: '收银员', color: 'green' },
          { id: 3, name: '张收银', role: '收银员', color: 'blue' }
        ];
      }
    },

    // 加载收款码列表
    async loadQRCodeList() {
      try {
        console.log('📋 加载收款码列表...');

        const response = await getQRConfigListNew();

        console.log('📥 收款码列表响应:', response);

        if (response && response.code === 0) {
          this.generatedCodes = (response.data || []).map(item => {
            console.log('📋 收款码原始数据:', item);
            return {
              id: item.id,
              name: item.name || '未命名收款码',
              position: item.staff_name || '商户自己',
              date: this.formatDate(new Date(item.addtime)) + '创建',
              color: this.getRandomColor(),
              staff_id: item.staff_id,
              style: item.qr_style || item.style, // 修复：优先使用qr_style字段
              description: item.description,
              status: item.status,
              precode: item.precode,
              qr_url: item.qr_url
            };
          });
          console.log('✅ 收款码列表加载成功:', this.generatedCodes.length, '个收款码');
          console.log('📋 处理后的收款码数据:', this.generatedCodes);
        } else {
          console.warn('⚠️ 收款码列表加载失败，使用空列表');
          this.generatedCodes = [];
        }
      } catch (error) {
        console.error('❌ 加载收款码列表失败:', error);
        this.generatedCodes = [];
      }
    },

    // 获取随机颜色
    getRandomColor() {
      const colors = ['blue-light', 'green-light', 'purple-light', 'orange-light', 'red-light'];
      return colors[Math.floor(Math.random() * colors.length)];
    },

    // 生成二维码图片
    async generateQRCode(styleName = 'native') {
      try {
        this.isGeneratingQR = true;
        console.log('🎨 生成二维码图片...', styleName);

        if (!this.qrCodeUrl) {
          console.error('❌ 收款码URL未生成');
          uni.showToast({
            title: '收款码URL未生成',
            icon: 'none'
          });
          return;
        }

        // 如果样式配置不存在，使用默认配置
        let styleConfig;
        if (!this.qrConfig || !this.qrConfig[styleName]) {
          console.warn('⚠️ 二维码样式配置不存在，使用默认配置:', styleName);
          styleConfig = {
            qrWidth: 300,
            qrHeight: 300,
            foreground: '#000000',
            background: '#FFFFFF'
          };
        } else {
          styleConfig = this.qrConfig[styleName];
        }

        // 生成二维码
        const qrCodeResult = await generateQRCode(this.qrCodeUrl, {
          width: parseInt(styleConfig.qrWidth) || 300,
          height: parseInt(styleConfig.qrHeight) || 300,
          foreground: styleConfig.foreground || '#000000',
          background: styleConfig.background || '#FFFFFF'
        });

        if (qrCodeResult) {
          console.log('✅ 二维码生成成功，类型:', typeof qrCodeResult);
          console.log('🔍 二维码内容预览:', qrCodeResult.substring(0, 100));

          // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
          // 小程序环境：如果是URL，需要下载为本地临时文件
          if (qrCodeResult.startsWith('http')) {
            console.log('📱 小程序环境，下载二维码图片...');
            try {
              const downloadResult = await new Promise((resolve, reject) => {
                uni.downloadFile({
                  url: qrCodeResult,
                  success: (res) => {
                    if (res.statusCode === 200) {
                      console.log('✅ 二维码图片下载成功:', res.tempFilePath);
                      resolve(res.tempFilePath);
                    } else {
                      reject(new Error(`下载失败，状态码: ${res.statusCode}`));
                    }
                  },
                  fail: reject
                });
              });
              this.qrCodeImage = downloadResult;
            } catch (downloadError) {
              console.error('❌ 下载二维码图片失败:', downloadError);
              // 直接使用URL，让image组件尝试加载
              this.qrCodeImage = qrCodeResult;
            }
          } else {
            this.qrCodeImage = qrCodeResult;
          }
          // #endif

          // #ifdef H5 || APP-PLUS
          // H5/APP环境：直接使用返回的结果
          this.qrCodeImage = qrCodeResult;
          // #endif

          this.selectedStyle = styleName;
          console.log('✅ 二维码图片设置完成:', this.qrCodeImage);
        } else {
          throw new Error('二维码生成返回空值');
        }

      } catch (error) {
        console.error('❌ 生成二维码图片失败:', error);

        // 尝试生成一个基础的二维码作为备用
        try {
          if (this.qrCodeUrl) {
            console.log('🔄 尝试生成备用二维码...');

            // 直接使用最简单的API
            const simpleQRUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(this.qrCodeUrl)}`;
            console.log('🔄 使用简单API:', simpleQRUrl);

            // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
            // 小程序环境：测试下载
            try {
              const downloadResult = await new Promise((resolve, reject) => {
                uni.downloadFile({
                  url: simpleQRUrl,
                  success: (res) => {
                    console.log('✅ 备用二维码下载结果:', res.statusCode, res.tempFilePath);
                    if (res.statusCode === 200) {
                      resolve(res.tempFilePath);
                    } else {
                      reject(new Error(`状态码: ${res.statusCode}`));
                    }
                  },
                  fail: (error) => {
                    console.error('❌ 备用二维码下载失败:', error);
                    reject(error);
                  }
                });
              });
              this.qrCodeImage = downloadResult;
              console.log('✅ 备用二维码设置成功');
              return;
            } catch (downloadError) {
              console.error('❌ 备用二维码下载失败:', downloadError);
              // 直接使用URL试试
              this.qrCodeImage = simpleQRUrl;
              console.log('⚠️ 直接使用二维码URL');
              return;
            }
            // #endif

            // #ifdef H5 || APP-PLUS
            this.qrCodeImage = simpleQRUrl;
            console.log('✅ H5环境直接使用二维码URL');
            return;
            // #endif
          }
        } catch (fallbackError) {
          console.error('❌ 备用二维码生成也失败:', fallbackError);
        }

        // 如果所有方法都失败，使用占位图片
        this.qrCodeImage = '/static/code/qrcode-sample.png';

        uni.showToast({
          title: '生成二维码失败',
          icon: 'none'
        });
      } finally {
        this.isGeneratingQR = false;
      }
    },

    goBack() {
      uni.navigateBack();
    },

    // WebSocket状态点击事件 - 🔧 重定向到语音设置页面
    onWebSocketStatus() {
      uni.navigateTo({
        url: '/pages/settings/voice'
      });
    },

    // 🔧 重新启用WebSocket状态更新
    updateWebSocketStatus() {
      // 从WebSocket Mixin获取状态
      this.websocketStatus = {
        connected: this.wsConnected || false,
        connecting: this.wsConnecting || false
      };
      console.log('🔄 收款码页面WebSocket状态更新:', this.websocketStatus);
    },

    // 获取语音设置
    getVoiceSettings() {
      return voiceManager.getStatus();
    },

    // 分享收款码
    shareCode() {
      if (this.qrCodeUrl) {
        // 分享收款码链接
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneSession',
          type: 0,
          href: this.qrCodeUrl,
          title: `扫码向${this.paymentCodeName}付款`,
          summary: '请使用微信或支付宝扫码付款',
          imageUrl: this.qrCodeImage,
          success: () => {
            uni.showToast({
              title: '分享成功',
              icon: 'success'
            });
          },
          fail: () => {
            // 如果分享失败，复制链接到剪贴板
            uni.setClipboardData({
              data: this.qrCodeUrl,
              success: () => {
                uni.showToast({
                  title: '链接已复制到剪贴板',
                  icon: 'success'
                });
              }
            });
          }
        });
      } else {
        uni.showToast({
          title: '收款码未生成',
          icon: 'none'
        });
      }
    },

    // 收款码名称相关
    showEditNameModal() {
      this.tempPaymentCodeName = this.paymentCodeName;
      this.showNameModal = true;
    },
    cancelEditName() {
      this.showNameModal = false;
    },
    async confirmEditName() {
      if (!this.tempPaymentCodeName.trim()) {
        uni.showToast({
          title: '收款码名称不能为空',
          icon: 'none'
        });
        return;
      }

      try {
        console.log('💾 保存收款码名称:', this.tempPaymentCodeName);

        // 检查登录状态
        const userToken = uni.getStorageSync('user_token');
        const userUid = uni.getStorageSync('user_uid');
        console.log('🔑 当前用户状态:', {
          hasToken: !!userToken,
          uid: userUid,
          tokenLength: userToken ? userToken.length : 0
        });

        if (!userToken || !userUid) {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          });
          uni.navigateTo({
            url: '/pages/login/index'
          });
          return;
        }

        const response = await request({
          url: '/user/ajax2.php?act=edit_codename',
          method: 'POST',
          data: {
            codename: this.tempPaymentCodeName
          }
        });

        console.log('📥 保存收款码名称响应:', response);

        if (response && response.code === 1) {
          this.paymentCodeName = this.tempPaymentCodeName;
          this.showNameModal = false;

          // 重新生成二维码
          await this.generateQRCode(this.selectedStyle);

          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });
        } else {
          const errorMsg = response?.msg || '保存失败';
          console.error('❌ 保存失败，服务器响应:', response);
          throw new Error(errorMsg);
        }
      } catch (error) {
        console.error('❌ 保存收款码名称失败:', error);

        // 更详细的错误处理
        let errorMessage = '保存失败';
        if (error.message) {
          errorMessage = error.message;
        } else if (error.msg) {
          errorMessage = error.msg;
        } else if (typeof error === 'string') {
          errorMessage = error;
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      }
    },

    // 保存二维码图片
    async saveQRCodeImage() {
      try {
        if (!this.qrCodeImage) {
          this.showToast('请先生成二维码', 'none');
          return;
        }

        console.log('💾 保存二维码图片...');

        // 检测运行环境并使用对应的保存方式
        // #ifdef H5
        // H5环境：使用浏览器下载
        this.downloadImageInBrowser();
        // #endif

        // #ifdef MP || APP-PLUS
        // 小程序和APP环境：使用uni-app API保存到相册
        await this.saveToAlbumNative();
        // #endif

      } catch (error) {
        console.error('❌ 保存二维码图片失败:', error);
        this.showSaveFailedModal();
      }
    },

    // H5环境下载图片
    downloadImageInBrowser() {
      // #ifdef H5
      try {
        if (typeof document === 'undefined') {
          throw new Error('document对象不可用');
        }

        const link = document.createElement('a');
        link.href = this.qrCodeImage;
        link.download = `qrcode_${Date.now()}.png`;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.showToast('开始下载', 'success');
      } catch (error) {
        console.error('H5下载失败:', error);
        this.showToast('下载失败，请右键保存图片', 'none');
      }
      // #endif

      // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
      this.showToast('小程序环境不支持直接下载，请长按保存', 'none');
      // #endif
    },

    // 小程序和APP环境保存到相册
    async saveToAlbumNative() {
      // 检查是否有相册权限
      try {
        await uni.authorize({
          scope: 'scope.writePhotosAlbum'
        });
      } catch (authError) {
        // 权限被拒绝，引导用户手动授权
        const modalResult = await uni.showModal({
          title: '需要相册权限',
          content: '保存图片需要访问您的相册，请在设置中开启权限',
          confirmText: '去设置',
          cancelText: '取消'
        });

        if (modalResult.confirm) {
          uni.openSetting();
        }
        return;
      }

      // 判断二维码图片类型并保存
      if (this.qrCodeImage.startsWith('data:image')) {
        // 处理base64格式的图片
        try {
          // SVG格式的特殊处理
          if (this.qrCodeImage.startsWith('data:image/svg+xml')) {
            uni.showModal({
              title: '保存图片',
              content: '请截图保存二维码，或长按二维码图片选择保存',
              showCancel: false,
              confirmText: '知道了'
            });
            return;
          }

          // 处理PNG/JPEG格式
          const base64Data = this.qrCodeImage.split(',')[1];
          const arrayBuffer = uni.base64ToArrayBuffer(base64Data);

          // 创建临时文件路径
          const tempFilePath = `${uni.env.USER_DATA_PATH || wx.env.USER_DATA_PATH}/qrcode_${Date.now()}.png`;

          // 写入临时文件
          await new Promise((resolve, reject) => {
            uni.getFileSystemManager().writeFile({
              filePath: tempFilePath,
              data: arrayBuffer,
              encoding: 'binary',
              success: resolve,
              fail: reject
            });
          });

          // 保存到相册
          await uni.saveImageToPhotosAlbum({
            filePath: tempFilePath
          });

          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });

        } catch (saveError) {
          console.error('保存base64图片失败:', saveError);
          this.showSaveFailedModal();
        }
      } else if (this.qrCodeImage.startsWith('http')) {
        // 处理网络图片URL
        try {
          const downloadResult = await uni.downloadFile({
            url: this.qrCodeImage
          });

          if (downloadResult.statusCode === 200) {
            await uni.saveImageToPhotosAlbum({
              filePath: downloadResult.tempFilePath
            });

            uni.showToast({
              title: '保存成功',
              icon: 'success'
            });
          } else {
            throw new Error('下载图片失败');
          }

        } catch (saveError) {
          console.error('保存网络图片失败:', saveError);
          this.showSaveFailedModal();
        }
      } else {
        this.showSaveFailedModal();
      }
    },

    // 统一的提示方法
    showToast(title, icon = 'none') {
      // #ifdef H5
      // H5环境使用console输出，也可以自定义提示组件
      if (icon === 'success') {
        console.log('✅ ' + title);
      } else {
        console.warn('⚠️ ' + title);
      }
      // #endif

      // #ifdef MP || APP-PLUS
      uni.showToast({
        title: title,
        icon: icon
      });
      // #endif
    },

    // 显示保存失败的提示
    showSaveFailedModal() {
      uni.showModal({
        title: '保存图片',
        content: '自动保存失败，请长按二维码图片手动保存到相册',
        showCancel: false,
        confirmText: '知道了'
      });
    },

    // 切换二维码样式
    async changeQRStyle(styleName) {
      if (styleName === this.selectedStyle) {
        return;
      }

      try {
        console.log('🎨 切换二维码样式:', styleName);
        await this.generateQRCode(styleName);

        // 保存用户选择的样式到本地
        uni.setStorageSync('qr_style_preference', styleName);

      } catch (error) {
        console.error('❌ 切换二维码样式失败:', error);
        uni.showToast({
          title: '切换样式失败',
          icon: 'none'
        });
      }
    },

    // 员工选择相关
    showStaffModal() {
      this.tempSelectedStaffId = this.selectedStaff?.id || null;
      this.showStaffListModal = true;
    },
    cancelSelectStaff() {
      this.showStaffListModal = false;
    },
    selectStaff(staff) {
      this.tempSelectedStaffId = staff.id;
    },
    async confirmSelectStaff() {
      try {
        const selectedStaff = this.staffList.find(item => item.id === this.tempSelectedStaffId);

        if (selectedStaff) {
          // 保存员工绑定到后端
          const configData = {
            staff_id: selectedStaff.id,
            name: this.paymentCodeName,
            qr_style: this.selectedStyle,
            description: `绑定员工: ${selectedStaff.name}`
          };

          console.log('💾 保存员工绑定配置:', configData);

          const response = await saveQRConfig(configData);
          console.log('📥 保存响应:', response);

          if (response && response.code === 0) {
            this.selectedStaff = selectedStaff;

            // 重新生成包含员工信息的二维码URL
            await this.generateQRCodeUrl();
            await this.generateQRCode(this.selectedStyle);

            uni.showToast({
              title: '绑定员工成功',
              icon: 'success'
            });
          } else {
            throw new Error(response?.msg || '绑定失败');
          }
        }

        this.showStaffListModal = false;
      } catch (error) {
        console.error('❌ 绑定员工失败:', error);
        uni.showToast({
          title: error.message || '绑定员工失败',
          icon: 'none'
        });
      }
    },

    // 二维码样式选择相关
    openStyleModal() {
      this.tempSelectedStyle = this.selectedStyle;
      this.showStyleModal = true;
    },
    cancelSelectStyle() {
      this.showStyleModal = false;
    },
    selectStyle(styleValue) {
      this.tempSelectedStyle = styleValue;
    },
    async confirmSelectStyle() {
      if (this.tempSelectedStyle !== this.selectedStyle) {
        await this.changeQRStyle(this.tempSelectedStyle);
      }
      this.showStyleModal = false;
    },

    // 获取样式标签
    getStyleLabel(styleValue) {
      const style = this.qrCodeStyles.find(item => item.value === styleValue);
      return style ? style.label : 'H5原生';
    },

    // 🎨 计算二维码覆盖层样式
    getQROverlayStyle(styleConfig) {
      if (!styleConfig) {
        return {
          left: '150rpx',
          top: '200rpx',
          width: '200rpx',
          height: '200rpx'
        };
      }

      // 设计稿尺寸：900x1200
      // 小程序显示容器尺寸：550rpx x 900rpx
      const designWidth = 900;
      const designHeight = 1200;
      const displayWidth = 550; // rpx - 容器宽度
      const displayHeight = 900; // rpx - 容器高度

      // 计算缩放比例
      const scaleX = displayWidth / designWidth;
      const scaleY = displayHeight / designHeight;

      // 使用较小的缩放比例以保持比例
      const scale = Math.min(scaleX, scaleY);

      // 计算位置和尺寸，二维码向下移动
      const left = Math.round((styleConfig.qrLeft || 270) * scale);
      const top = Math.round((styleConfig.qrTop || 320) * scale + 40); // 向下移动40rpx
      const width = Math.round((styleConfig.qrWidth || 370) * scale * 0.9); // 增大到90%
      const height = Math.round((styleConfig.qrHeight || 370) * scale * 0.9); // 增大到90%

      console.log('🎨 二维码位置计算:', {
        样式名称: styleConfig.styleName || '未知',
        设计稿尺寸: { width: designWidth, height: designHeight },
        显示区域: { width: displayWidth, height: displayHeight },
        缩放比例: scale.toFixed(3),
        原始配置: {
          left: styleConfig.qrLeft,
          top: styleConfig.qrTop,
          width: styleConfig.qrWidth,
          height: styleConfig.qrHeight
        },
        最终样式: { left, top, width, height }
      });

      return {
        left: left + 'rpx',
        top: top + 'rpx',
        width: width + 'rpx',
        height: height + 'rpx'
      };
    },
    // 🎨 显示样式化二维码（查看模式）
    async showStyledQRCode() {
      try {
        console.log('🎨 显示样式化二维码查看弹窗');

        // 确保有二维码URL
        if (!this.qrCodeUrl) {
          console.error('❌ 收款码URL未生成');
          uni.showToast({
            title: '收款码未生成',
            icon: 'none'
          });
          return;
        }

        // 准备查看数据
        this.viewCodeData = {
          name: this.paymentCodeName,
          staff: this.selectedStaff,
          style: this.selectedStyle,
          qr_url: this.qrCodeUrl,
          description: '当前收款码'
        };

        // 显示查看弹窗
        this.showViewModal = true;

        // 生成样式化二维码用于查看
        await this.generateStyledViewQRCode(this.qrCodeUrl, this.selectedStyle);

      } catch (error) {
        console.error('❌ 显示样式化二维码失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    },

    // 🎨 生成样式化二维码
    async generateStyledQRCode() {
      try {
        this.isGeneratingStyledQR = true;
        console.log('🎨 生成样式化二维码...', this.selectedStyle);

        if (!this.qrCodeUrl) {
          throw new Error('收款码URL未生成');
        }

        // 如果是原生样式，直接使用简单二维码
        if (this.selectedStyle === 'native') {
          this.styledQRCodeImage = this.qrCodeImage;
          console.log('✅ 使用原生样式二维码');
          return;
        }

        // 🔧 关键修复：将外部二维码URL转换为base64格式以避免跨域问题
        let qrCodeBase64;
        try {
          console.log('🔄 转换二维码为base64格式...');
          qrCodeBase64 = await this.convertImageToBase64(this.qrCodeImage);
          console.log('✅ 二维码转换为base64成功');
        } catch (error) {
          console.error('❌ 二维码转换base64失败:', error);
          // 如果转换失败，直接使用原始图片
          qrCodeBase64 = this.qrCodeImage;
        }

        // 第一步：从后端获取样式配置
        console.log('🔄 从后端获取样式配置...');
        let styleConfig;

        try {
          // 优先从已加载的配置中获取
          if (this.qrConfig && this.qrConfig[this.selectedStyle]) {
            styleConfig = this.qrConfig[this.selectedStyle];
            console.log('✅ 使用已加载的样式配置:', styleConfig);
          } else {
            // 如果没有加载配置，重新加载
            console.log('🔄 重新加载样式配置...');
            await this.loadQRConfig();
            styleConfig = this.qrConfig[this.selectedStyle];
          }

          if (!styleConfig) {
            throw new Error(`样式配置不存在: ${this.selectedStyle}`);
          }
        } catch (configError) {
          console.error('❌ 获取样式配置失败:', configError);
          // 使用基础二维码作为备用方案
          this.styledQRCodeImage = this.qrCodeImage;
          return;
        }

        // 第二步：创建样式化二维码数据
        const styleData = {
          merchant_name: this.paymentCodeName,
          qr_url: qrCodeBase64, // 使用转换后的base64二维码图片
          style_config: styleConfig,
          staff_name: this.selectedStaff ? this.selectedStaff.name : '',
          amount: this.amount || ''
        };

        console.log('🎨 样式数据:', styleData);

        try {
          console.log('🎨 开始Canvas合成样式化二维码...');
          // 第三步：使用Canvas合成样式化二维码
          const styledImage = await this.compositeStyledQRCode(styleData);
          this.styledQRCodeImage = styledImage;
          console.log('✅ 样式化二维码生成成功');
        } catch (error) {
          console.error('❌ Canvas合成失败，使用简单样式版本:', error);
          console.error('❌ 详细错误信息:', error.message, error.stack);
          // 如果Canvas合成失败，使用简单的样式版本
          this.styledQRCodeImage = await this.createSimpleStyledQRCode(styleData);
        }

      } catch (error) {
        console.error('❌ 生成样式化二维码失败:', error);

        // 失败时使用基础二维码
        this.styledQRCodeImage = this.qrCodeImage;

        uni.showToast({
          title: '使用基础样式',
          icon: 'none'
        });
      } finally {
        this.isGeneratingStyledQR = false;
      }
    },

    // 🎨 创建简单样式化二维码（备用方案）
    async createSimpleStyledQRCode(styleData) {
      return new Promise((resolve) => {
        try {
          console.log('🎨 开始创建简单样式二维码');
          const { merchant_name } = styleData;

          // #ifdef H5
          if (typeof document === 'undefined') {
            console.warn('⚠️ 小程序环境不支持Canvas，使用简化版本');
            resolve(this.qrCodeImage);
            return;
          }

          // 创建canvas
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            console.error('❌ 无法创建Canvas上下文');
            resolve(this.qrCodeImage);
            return;
          }
          // #endif

          // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
          console.log('📱 小程序环境，直接返回原始二维码');
          resolve(this.qrCodeImage);
          return;
          // #endif

          // 设置canvas尺寸
          canvas.width = 400;
          canvas.height = 500;

          // 绘制白色背景
          ctx.fillStyle = '#ffffff';
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // 绘制渐变背景
          const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
          gradient.addColorStop(0, '#667eea');
          gradient.addColorStop(1, '#764ba2');
          ctx.fillStyle = gradient;
          ctx.fillRect(0, 0, canvas.width, 120);

          // 绘制商户名称
          if (merchant_name) {
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 24px Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(merchant_name, canvas.width / 2, 70);
          }

          // 加载并绘制二维码
          const qrImg = new Image();

          // 设置跨域属性 - 关键修复！
          qrImg.crossOrigin = 'anonymous';
          console.log('🔒 简单样式二维码图片设置跨域属性: anonymous');

          qrImg.onload = () => {
            try {
              console.log('✅ 二维码图片加载成功，开始绘制');
              // 绘制白色背景圆角矩形
              const qrSize = 280;
              const qrX = (canvas.width - qrSize) / 2;
              const qrY = 150;

              ctx.fillStyle = '#ffffff';
              ctx.shadowColor = 'rgba(0, 0, 0, 0.1)';
              ctx.shadowBlur = 10;
              ctx.shadowOffsetY = 5;

              // 绘制圆角矩形背景
              this.drawRoundedRect(ctx, qrX - 20, qrY - 20, qrSize + 40, qrSize + 40, 20);
              ctx.fill();

              // 重置阴影
              ctx.shadowColor = 'transparent';
              ctx.shadowBlur = 0;
              ctx.shadowOffsetY = 0;

              // 绘制二维码
              ctx.drawImage(qrImg, qrX, qrY, qrSize, qrSize);

              // 添加底部文字
              ctx.fillStyle = '#666666';
              ctx.font = '16px Arial, sans-serif';
              ctx.textAlign = 'center';
              ctx.fillText('扫码支付', canvas.width / 2, 470);

              const styledQRImage = canvas.toDataURL('image/png');
              console.log('✅ 简单样式二维码创建成功');
              resolve(styledQRImage);
            } catch (error) {
              console.error('❌ 绘制简单样式二维码失败:', error);
              resolve(this.qrCodeImage);
            }
          };

          qrImg.onerror = (error) => {
            console.error('❌ 二维码图片加载失败:', error);
            resolve(this.qrCodeImage);
          };

          console.log('🔄 开始加载二维码图片...');
          qrImg.src = this.qrCodeImage;

        } catch (error) {
          console.error('❌ 创建简单样式二维码失败:', error);
          resolve(this.qrCodeImage);
        }
      });
    },

    // 绘制圆角矩形
    drawRoundedRect(ctx, x, y, width, height, radius) {
      try {
        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
      } catch (error) {
        console.error('❌ 绘制圆角矩形失败:', error);
        // 如果圆角矩形绘制失败，绘制普通矩形
        ctx.fillRect(x, y, width, height);
      }
    },

    // 🎨 合成样式化二维码图片
    async compositeStyledQRCode(styleData) {
      return new Promise((resolve, reject) => {
        try {
          const { style_config, merchant_name } = styleData;
          console.log('🎨 开始合成样式化二维码:', style_config);
          console.log('🎨 商户名称:', merchant_name);
          console.log('🎨 二维码图片URL:', this.qrCodeImage);

          // 检查必要的配置
          if (!style_config || !style_config.url) {
            console.error('❌ 样式配置缺少背景图片URL');
            reject(new Error('样式配置缺少背景图片URL'));
            return;
          }

          // 创建canvas
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            console.error('❌ 无法创建Canvas上下文');
            reject(new Error('无法创建Canvas上下文'));
            return;
          }

          // 设置canvas尺寸
          canvas.width = parseInt(style_config.imgWidth) || 900;
          canvas.height = parseInt(style_config.imgHeight) || 1200;
          console.log('📐 Canvas尺寸:', canvas.width + 'x' + canvas.height);

          // 处理背景图片URL
          let backgroundUrl = style_config.url;
          console.log('🔄 背景图片URL:', backgroundUrl);

          // 加载背景图片
          const backgroundImg = new Image();

          // 设置跨域属性 - 关键修复！对所有图片都设置
          backgroundImg.crossOrigin = 'anonymous';
          console.log('🔒 背景图片设置跨域属性: anonymous');

          // 设置超时机制
          const loadTimeout = setTimeout(() => {
            console.error('❌ 背景图片加载超时');
            // 超时后使用简单样式
            this.createSimpleStyledQRCode(styleData).then(resolve).catch(reject);
          }, 10000); // 10秒超时

          backgroundImg.onload = () => {
            clearTimeout(loadTimeout);
            console.log('✅ 背景图片加载成功');
            try {
              // 绘制背景图片
              ctx.drawImage(backgroundImg, 0, 0, canvas.width, canvas.height);
              console.log('✅ 背景图片绘制完成');

              // 加载二维码图片
              const qrImg = new Image();

              // 设置跨域属性 - 关键修复！
              qrImg.crossOrigin = 'anonymous';
              console.log('🔒 二维码图片设置跨域属性: anonymous');

              qrImg.onload = () => {
                console.log('✅ 二维码图片加载成功');
                try {
                  // 计算二维码位置和尺寸
                  const qrWidth = parseInt(style_config.qrWidth) || 300;
                  const qrHeight = parseInt(style_config.qrHeight) || 300;
                  const qrLeft = parseInt(style_config.qrLeft) || 0;
                  const qrTop = parseInt(style_config.qrTop) || 0;

                  console.log('📍 二维码位置和尺寸:', { qrLeft, qrTop, qrWidth, qrHeight });

                  // 绘制二维码
                  ctx.drawImage(qrImg, qrLeft, qrTop, qrWidth, qrHeight);
                  console.log('✅ 二维码绘制完成');

                  // 如果有商户名称且配置了文字样式，绘制商户名称
                  if (merchant_name && style_config.fontColor && style_config.fontColor !== 'transparent') {
                    ctx.fillStyle = style_config.fontColor;
                    ctx.font = style_config.font || '70px Arial';
                    ctx.textAlign = 'center';

                    const textX = canvas.width / 2;
                    const textY = parseInt(style_config.recNameTop) || 178;

                    console.log('📝 绘制商户名称:', merchant_name, '位置:', textX, textY);
                    ctx.fillText(merchant_name, textX, textY);
                  }

                  // 转换为base64 - 处理跨域污染问题
                  try {
                    const styledQRImage = canvas.toDataURL('image/png');
                    console.log('✅ 样式化二维码合成完成');
                    resolve(styledQRImage);
                  } catch (canvasError) {
                    console.error('❌ Canvas被跨域图片污染，无法导出:', canvasError);
                    console.log('🔄 尝试使用备用方案...');

                    // 备用方案：使用简单样式或返回原始二维码
                    this.createSimpleStyledQRCode(styleData).then(resolve).catch(() => {
                      console.log('🔄 备用方案失败，返回原始二维码');
                      resolve(this.qrCodeImage);
                    });
                  }

                } catch (error) {
                  console.error('❌ 绘制二维码失败:', error);
                  reject(error);
                }
              };

              qrImg.onerror = (error) => {
                console.error('❌ 二维码图片加载失败:', error);
                reject(new Error('二维码图片加载失败'));
              };

              console.log('🔄 开始加载二维码图片...');
              qrImg.src = styleData.qr_url; // 使用传入的二维码图片URL

            } catch (error) {
              console.error('❌ 绘制背景失败:', error);
              reject(error);
            }
          };

          backgroundImg.onerror = (error) => {
            clearTimeout(loadTimeout);
            console.error('❌ 背景图片加载失败:', backgroundUrl, error);
            console.error('❌ 错误详情:', {
              url: backgroundUrl,
              crossOrigin: backgroundImg.crossOrigin,
              error: error
            });

            // 如果背景图片加载失败，使用简单样式作为备用方案
            console.log('🔄 背景图片加载失败，使用简单样式备用方案');
            this.createSimpleStyledQRCode(styleData).then(resolve).catch(() => {
              // 如果简单样式也失败，返回原始二维码
              console.log('🔄 简单样式也失败，返回原始二维码');
              resolve(this.qrCodeImage);
            });
          };

          console.log('🔄 开始加载背景图片:', backgroundUrl);
          backgroundImg.src = backgroundUrl;

        } catch (error) {
          console.error('❌ 创建样式化二维码失败:', error);
          reject(error);
        }
      });
    },

    // 🎨 关闭样式化二维码弹窗
    closeStyledQRModal() {
      this.showStyledQRModal = false;
      this.styledQRCodeImage = '';
    },

    // 🎨 保存样式化二维码
    async saveStyledQRCode() {
      if (!this.styledQRCodeImage) {
        uni.showToast({
          title: '请先生成二维码',
          icon: 'none'
        });
        return;
      }

      // 复用现有的保存逻辑
      const originalImage = this.qrCodeImage;
      this.qrCodeImage = this.styledQRCodeImage;
      await this.saveQRCodeImage();
      this.qrCodeImage = originalImage;
    },

    // 🎨 分享样式化二维码
    shareStyledQRCode() {
      if (!this.styledQRCodeImage) {
        uni.showToast({
          title: '请先生成二维码',
          icon: 'none'
        });
        return;
      }

      // 分享样式化二维码
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        href: this.qrCodeUrl,
        title: `扫码向${this.paymentCodeName}付款`,
        summary: '请使用微信或支付宝扫码付款',
        imageUrl: this.styledQRCodeImage,
        success: () => {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
        },
        fail: () => {
          // 如果分享失败，复制链接到剪贴板
          uni.setClipboardData({
            data: this.qrCodeUrl,
            success: () => {
              uni.showToast({
                title: '链接已复制到剪贴板',
                icon: 'success'
              });
            }
          });
        }
      });
    },

    createNewCode() {
      // 重置创建表单
      this.createForm = {
        name: '',
        style: 'native',
        staff_id: null,
        fixed_amount: '',
        description: '',
        precode: ''
      };
      this.showCreateModal = true;
    },

    // 🎯 查看收款码
    async viewCode(code) {
      try {
        console.log('👁️ 查看收款码:', code);

        // 显示加载提示
        uni.showLoading({ title: '加载中...' });

        // 设置查看数据
        this.viewCodeData = {
          id: code.id,
          name: code.name,
          position: code.position,
          date: code.date,
          description: code.description,
          style: code.style || 'native',
          staff_id: code.staff_id,
          qr_url: code.qr_url
        };

        // 显示弹窗
        this.showViewModal = true;

        // 生成二维码图片
        await this.generateViewQRCode();

        // 隐藏加载提示
        uni.hideLoading();

      } catch (error) {
        console.error('❌ 查看收款码失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    },

    // 🎯 生成查看用的样式化收款码
    async generateViewQRCode() {
      try {
        this.isLoadingViewQR = true;
        console.log('🎨 生成样式化收款码...');

        // 构建收款码URL
        let qrUrl = '';
        if (this.viewCodeData.qr_url) {
          qrUrl = this.viewCodeData.qr_url;
        } else {
          // 如果没有保存的URL，重新构建
          const uid = uni.getStorageSync('user_uid');
          qrUrl = `http://ceshi.huisas.com/paypage/?uid=${uid}`;
          if (this.viewCodeData.staff_id) {
            qrUrl += `&staff_id=${this.viewCodeData.staff_id}`;
          }
        }

        console.log('🔗 收款码URL:', qrUrl);

        // 获取样式配置
        const styleName = this.viewCodeData.style || 'native';

        // 如果是native样式，生成简单二维码
        if (styleName === 'native') {
          const qrCodeBase64 = await generateQRCode(qrUrl, {
            width: 300,
            height: 300,
            foreground: '#000000',
            background: '#FFFFFF'
          });
          this.viewQRCodeImage = qrCodeBase64;
        } else {
          // 生成样式化的聚合收款码
          await this.generateStyledCompositeQR(qrUrl, styleName);
        }

        console.log('✅ 样式化收款码生成成功');

      } catch (error) {
        console.error('❌ 生成样式化收款码失败:', error);
        this.viewQRCodeImage = '/static/code/qrcode-sample.png';
        uni.showToast({
          title: '生成收款码失败',
          icon: 'none'
        });
      } finally {
        this.isLoadingViewQR = false;
      }
    },

    // 🎨 生成样式化聚合收款码
    async generateStyledCompositeQR(qrUrl, styleName) {
      try {
        console.log('🎨 生成样式化聚合收款码:', styleName);

        // 获取样式配置
        const styleConfig = this.qrConfig && this.qrConfig[styleName] ? this.qrConfig[styleName] : null;

        if (!styleConfig) {
          console.warn('⚠️ 样式配置不存在，使用默认样式');
          // 生成简单二维码作为备用
          const qrCodeBase64 = await generateQRCode(qrUrl, {
            width: 300,
            height: 300,
            foreground: '#000000',
            background: '#FFFFFF'
          });
          this.viewQRCodeImage = qrCodeBase64;
          return;
        }

        // 🔧 小程序环境兼容性处理
        // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
        console.log('📱 小程序环境，使用简化的样式化二维码生成');
        await this.generateStyledQRCodeForMiniProgram(qrUrl, styleName, styleConfig);
        return;
        // #endif

        // #ifdef H5 || APP-PLUS
        // H5环境使用Canvas生成样式化二维码
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        // #endif

        // 设置画布尺寸
        const canvasWidth = parseInt(styleConfig.imgWidth) || 900;
        const canvasHeight = parseInt(styleConfig.imgHeight) || 1200;
        canvas.width = canvasWidth;
        canvas.height = canvasHeight;

        // 加载背景图片
        const bgImage = new Image();
        bgImage.crossOrigin = 'anonymous';

        await new Promise((resolve, reject) => {
          bgImage.onload = resolve;
          bgImage.onerror = reject;
          bgImage.src = styleConfig.url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTAwIiBoZWlnaHQ9IjEyMDAiIHZpZXdCb3g9IjAgMCA5MDAgMTIwMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjkwMCIgaGVpZ2h0PSIxMjAwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjQ1MCIgeTE9IjAiIHgyPSI0NTAiIHkyPSIxMjAwIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiM2NjY2RkYiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjOTk5OUZGIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+';
        });

        // 绘制背景
        ctx.drawImage(bgImage, 0, 0, canvasWidth, canvasHeight);

        // 生成二维码
        const qrCodeBase64 = await generateQRCode(qrUrl, {
          width: parseInt(styleConfig.qrWidth) || 550,
          height: parseInt(styleConfig.qrHeight) || 550,
          foreground: styleConfig.foreground || '#000000',
          background: styleConfig.background || '#FFFFFF'
        });

        // 加载二维码图片
        const qrImage = new Image();
        qrImage.crossOrigin = 'anonymous';

        await new Promise((resolve, reject) => {
          qrImage.onload = resolve;
          qrImage.onerror = reject;
          qrImage.src = qrCodeBase64;
        });

        // 绘制二维码
        const qrLeft = parseInt(styleConfig.qrLeft) || 145;
        const qrTop = parseInt(styleConfig.qrTop) || 350;
        const qrWidth = parseInt(styleConfig.qrWidth) || 550;
        const qrHeight = parseInt(styleConfig.qrHeight) || 550;

        ctx.drawImage(qrImage, qrLeft, qrTop, qrWidth, qrHeight);

        // 绘制收款人名称
        if (styleConfig.font && styleConfig.fontColor) {
          ctx.font = styleConfig.font;
          ctx.fillStyle = styleConfig.fontColor;
          ctx.textAlign = 'center';

          const nameText = this.viewCodeData.name || this.merchantName || '商家收款';
          const nameTop = parseInt(styleConfig.recNameTop) || 178;

          ctx.fillText(nameText, canvasWidth / 2, nameTop);
        }

        // 转换为base64
        this.viewQRCodeImage = canvas.toDataURL('image/png', 0.9);
        console.log('✅ 样式化聚合收款码生成成功');

      } catch (error) {
        console.error('❌ 生成样式化聚合收款码失败:', error);
        // 生成简单二维码作为备用
        const qrCodeBase64 = await generateQRCode(qrUrl, {
          width: 300,
          height: 300,
          foreground: '#000000',
          background: '#FFFFFF'
        });
        this.viewQRCodeImage = qrCodeBase64;
      }
    },

    // 🎯 小程序环境专用样式化二维码生成
    async generateStyledQRCodeForMiniProgram(qrUrl, styleName, styleConfig) {
      try {
        console.log('📱 小程序生成样式化二维码:', styleName, styleConfig);

        // 如果没有背景图片，生成简单二维码
        if (!styleConfig.url) {
          console.log('⚠️ 没有背景图片，生成简单二维码');
          const qrCodeBase64 = await generateQRCode(qrUrl, {
            width: parseInt(styleConfig.qrWidth) || 300,
            height: parseInt(styleConfig.qrHeight) || 300,
            foreground: styleConfig.foreground || '#000000',
            background: styleConfig.background || '#FFFFFF'
          });
          this.viewQRCodeImage = qrCodeBase64;
          return;
        }

        // 小程序环境暂时使用简化方案：直接显示背景图片和二维码的组合
        console.log('🎨 小程序环境使用简化的样式化二维码方案');

        // 生成二维码URL
        const qrCodeUrl = await generateQRCode(qrUrl, {
          width: parseInt(styleConfig.qrWidth) || 300,
          height: parseInt(styleConfig.qrHeight) || 300,
          foreground: styleConfig.foreground || '#000000',
          background: styleConfig.background || '#FFFFFF'
        });

        // 在小程序中，我们使用CSS定位的方式来实现样式化效果
        // 设置背景图片和二维码URL，让模板来处理显示
        this.viewQRCodeImage = qrCodeUrl;
        this.viewBackgroundImage = styleConfig.url;
        this.viewStyleConfig = {
          ...styleConfig,
          // 确保使用正确的位置属性名
          qrLeft: styleConfig.qrLeft || styleConfig.qrX || 300,
          qrTop: styleConfig.qrTop || styleConfig.qrY || 400
        };

        console.log('🎨 小程序样式化二维码配置:', {
          样式名称: styleName,
          背景图片: styleConfig.url,
          二维码位置: {
            left: styleConfig.qrLeft,
            top: styleConfig.qrTop,
            width: styleConfig.qrWidth,
            height: styleConfig.qrHeight
          }
        });

        console.log('✅ 小程序样式化二维码生成成功');

      } catch (error) {
        console.error('❌ 小程序样式化二维码生成失败:', error);
        // 生成简单二维码作为备用
        const qrCodeBase64 = await generateQRCode(qrUrl, {
          width: 300,
          height: 300,
          foreground: '#000000',
          background: '#FFFFFF'
        });
        this.viewQRCodeImage = qrCodeBase64;
      }
    },

    // 🎯 生成用于查看的样式化二维码
    async generateStyledViewQRCode(qrUrl, styleName) {
      try {
        console.log('🎨 生成查看用样式化二维码:', qrUrl, styleName);

        // 如果是native样式，生成简单二维码
        if (styleName === 'native') {
          const qrCodeBase64 = await generateQRCode(qrUrl, {
            width: 300,
            height: 300,
            foreground: '#000000',
            background: '#FFFFFF'
          });
          this.viewQRCodeImage = qrCodeBase64;
          return;
        }

        // 获取样式配置
        const styleConfig = this.qrConfig && this.qrConfig[styleName] ? this.qrConfig[styleName] : null;

        if (!styleConfig) {
          console.warn('⚠️ 样式配置不存在，使用默认样式');
          const qrCodeBase64 = await generateQRCode(qrUrl, {
            width: 300,
            height: 300,
            foreground: '#000000',
            background: '#FFFFFF'
          });
          this.viewQRCodeImage = qrCodeBase64;
          return;
        }

        // 🔧 小程序环境兼容性处理
        // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
        console.log('📱 小程序环境，生成查看用样式化二维码');
        await this.generateStyledQRCodeForMiniProgram(qrUrl, styleName, styleConfig);
        // #endif

        // #ifdef H5 || APP-PLUS
        // H5环境使用Canvas生成样式化二维码
        await this.generateStyledQRCodeForH5(qrUrl, styleName, styleConfig);
        // #endif

        console.log('✅ 查看用样式化二维码生成成功');

      } catch (error) {
        console.error('❌ 生成查看用样式化二维码失败:', error);
        this.viewQRCodeImage = '/static/code/qrcode-sample.png';
        uni.showToast({
          title: '生成二维码失败',
          icon: 'none'
        });
      }
    },

    // 🎯 H5环境专用样式化二维码生成
    async generateStyledQRCodeForH5(qrUrl, styleName, styleConfig) {
      try {
        console.log('🌐 H5环境生成样式化二维码:', styleName, styleConfig);

        // 如果没有背景图片URL，生成简单二维码
        if (!styleConfig.url) {
          console.log('⚠️ 没有背景图片，生成简单二维码');
          const qrCodeBase64 = await generateQRCode(qrUrl, {
            width: parseInt(styleConfig.qrWidth) || 300,
            height: parseInt(styleConfig.qrHeight) || 300,
            foreground: styleConfig.foreground || '#000000',
            background: styleConfig.background || '#FFFFFF'
          });
          this.viewQRCodeImage = qrCodeBase64;
          return;
        }

        // 创建Canvas进行样式化处理
        if (typeof document === 'undefined') {
          throw new Error('H5环境document不可用');
        }

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          throw new Error('无法创建Canvas上下文');
        }

        // 设置画布尺寸
        const canvasWidth = parseInt(styleConfig.imgWidth) || 900;
        const canvasHeight = parseInt(styleConfig.imgHeight) || 1200;
        canvas.width = canvasWidth;
        canvas.height = canvasHeight;

        // 加载背景图片
        const backgroundImg = new Image();
        backgroundImg.crossOrigin = 'anonymous';

        await new Promise((resolve, reject) => {
          backgroundImg.onload = () => {
            console.log('✅ 背景图片加载成功:', styleConfig.url);
            resolve();
          };
          backgroundImg.onerror = (error) => {
            console.error('❌ 背景图片加载失败:', styleConfig.url, error);
            reject(new Error('背景图片加载失败'));
          };

          // 设置超时
          setTimeout(() => {
            reject(new Error('背景图片加载超时'));
          }, 10000);

          backgroundImg.src = styleConfig.url;
        });

        // 绘制背景图片
        ctx.drawImage(backgroundImg, 0, 0, canvasWidth, canvasHeight);
        console.log('✅ 背景图片绘制完成');

        // 生成二维码
        const qrCodeBase64 = await generateQRCode(qrUrl, {
          width: parseInt(styleConfig.qrWidth) || 300,
          height: parseInt(styleConfig.qrHeight) || 300,
          foreground: styleConfig.foreground || '#000000',
          background: styleConfig.background === 'transparent' ? 'rgba(0,0,0,0)' : (styleConfig.background || '#FFFFFF')
        });

        // 加载二维码图片
        const qrImg = new Image();
        qrImg.crossOrigin = 'anonymous';

        await new Promise((resolve, reject) => {
          qrImg.onload = resolve;
          qrImg.onerror = () => reject(new Error('二维码图片加载失败'));
          qrImg.src = qrCodeBase64;
        });

        // 绘制二维码到指定位置
        const qrLeft = parseInt(styleConfig.qrLeft) || 0;
        const qrTop = parseInt(styleConfig.qrTop) || 0;
        const qrWidth = parseInt(styleConfig.qrWidth) || 300;
        const qrHeight = parseInt(styleConfig.qrHeight) || 300;

        ctx.drawImage(qrImg, qrLeft, qrTop, qrWidth, qrHeight);

        // 添加商家名称文字（如果需要）
        if (styleConfig.fontColor && styleConfig.fontColor !== 'transparent') {
          ctx.font = styleConfig.font || '70px 黑体';
          ctx.fillStyle = styleConfig.fontColor;
          ctx.textAlign = 'center';

          const textX = canvasWidth / 2;
          const textY = parseInt(styleConfig.recNameTop) || 178;

          ctx.fillText(this.merchantName || '商家收款', textX, textY);
        }

        // 转换为Base64
        this.viewQRCodeImage = canvas.toDataURL('image/png', 0.9);
        console.log('✅ H5样式化二维码生成成功，图片大小:', this.viewQRCodeImage.length, '字符');
        console.log('🎨 样式化二维码预览:', this.viewQRCodeImage.substring(0, 100) + '...');

      } catch (error) {
        console.error('❌ H5样式化二维码生成失败:', error);
        // 生成简单二维码作为备用
        try {
          const qrCodeBase64 = await generateQRCode(qrUrl, {
            width: 300,
            height: 300,
            foreground: '#000000',
            background: '#FFFFFF'
          });
          this.viewQRCodeImage = qrCodeBase64;
        } catch (fallbackError) {
          console.error('❌ 备用二维码生成也失败:', fallbackError);
          this.viewQRCodeImage = '/static/code/qrcode-sample.png';
        }
      }
    },

    // 🔧 二维码图片加载成功
    onQRImageLoad(e) {
      console.log('✅ 二维码图片加载成功:', this.qrCodeImage);
    },

    // 🔧 二维码图片加载失败
    onQRImageError(e) {
      console.error('❌ 二维码图片加载失败:', this.qrCodeImage, e);

      // 防止循环生成，直接使用占位图片
      if (this.qrCodeImage !== '/static/code/qrcode-sample.png') {
        console.log('⚠️ 设置为占位图片，避免循环生成');
        this.qrCodeImage = '/static/code/qrcode-sample.png';
      }
    },

    // 🎯 取消查看
    cancelViewCode() {
      this.showViewModal = false;
      this.viewCodeData = {};
      this.viewQRCodeImage = '';
      this.viewBackgroundImage = '';
      this.viewStyleConfig = null;
    },

    // 🎯 复制收款链接
    copyViewQRLink() {
      try {
        // 构建收款链接
        let shareUrl = '';
        if (this.viewCodeData.qr_url) {
          shareUrl = this.viewCodeData.qr_url;
        } else {
          const uid = uni.getStorageSync('user_uid');
          shareUrl = `http://ceshi.huisas.com/paypage/?uid=${uid}`;
          if (this.viewCodeData.staff_id) {
            shareUrl += `&staff_id=${this.viewCodeData.staff_id}`;
          }
        }

        // 复制到剪贴板
        uni.setClipboardData({
          data: shareUrl,
          success: () => {
            uni.showToast({
              title: '链接已复制',
              icon: 'success'
            });
          },
          fail: () => {
            uni.showToast({
              title: '复制失败',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        console.error('❌ 复制链接失败:', error);
        uni.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    },

    // 🎯 获取短链接显示
    getShortUrl(url) {
      if (!url) {
        const uid = uni.getStorageSync('user_uid');
        url = `http://ceshi.huisas.com/paypage/?uid=${uid}`;
        if (this.viewCodeData.staff_id) {
          url += `&staff_id=${this.viewCodeData.staff_id}`;
        }
      }

      // 如果链接太长，显示省略版本
      if (url.length > 40) {
        return url.substring(0, 35) + '...';
      }
      return url;
    },

    // 🎯 保存查看的二维码
    async saveViewQRCode() {
      if (!this.viewQRCodeImage) {
        uni.showToast({
          title: '请先生成二维码',
          icon: 'none'
        });
        return;
      }

      // 复用现有的保存逻辑
      const originalImage = this.qrCodeImage;
      this.qrCodeImage = this.viewQRCodeImage;
      await this.saveQRCodeImage();
      this.qrCodeImage = originalImage;
    },

    // 🎯 分享查看的二维码
    shareViewQRCode() {
      if (!this.viewQRCodeImage) {
        uni.showToast({
          title: '请先生成二维码',
          icon: 'none'
        });
        return;
      }

      // 构建分享URL
      let shareUrl = '';
      if (this.viewCodeData.qr_url) {
        shareUrl = this.viewCodeData.qr_url;
      } else {
        const uid = uni.getStorageSync('user_uid');
        shareUrl = `http://ceshi.huisas.com/paypage/?uid=${uid}`;
        if (this.viewCodeData.staff_id) {
          shareUrl += `&staff_id=${this.viewCodeData.staff_id}`;
        }
      }

      // 分享
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        href: shareUrl,
        title: `扫码向${this.viewCodeData.name}付款`,
        summary: '请使用微信或支付宝扫码付款',
        imageUrl: this.viewQRCodeImage,
        success: () => {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
        },
        fail: () => {
          // 如果分享失败，复制链接到剪贴板
          uni.setClipboardData({
            data: shareUrl,
            success: () => {
              uni.showToast({
                title: '链接已复制到剪贴板',
                icon: 'success'
              });
            }
          });
        }
      });
    },

    editCode(code) {
      // 填充编辑表单
      console.log('📝 编辑收款码数据:', code);
      this.editForm = {
        id: code.id,
        name: code.name,
        style: code.style, // 不设置默认值，保持原始值
        staff_id: code.staff_id,
        fixed_amount: '',
        description: code.description || '',
        precode: code.precode || ''
      };
      console.log('📝 编辑表单数据:', this.editForm);
      this.showEditModal = true;
    },
    async deleteCode(code) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除收款码 "' + code.name + '" 吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              console.log('🗑️ 删除收款码:', code.id);

              const response = await deleteQRConfig(code.id);

              console.log('📥 删除响应:', response);

              if (response && response.code === 0) {
                // 从列表中移除
                this.generatedCodes = this.generatedCodes.filter(item => item.id !== code.id);
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
              } else {
                throw new Error(response?.msg || '删除失败');
              }
            } catch (error) {
              console.error('❌ 删除收款码失败:', error);
              uni.showToast({
                title: error.message || '删除失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    showBindEmptyCodeModal() {
      this.showBindCodeModal = true;
      this.showManualInput = false;
      this.manualCodeInput = '';
    },
    cancelBindCode() {
      this.showBindCodeModal = false;
    },
    scanBindCode() {
      this.showBindCodeModal = false;

      // 调用扫码API
      uni.scanCode({
        onlyFromCamera: true,
        scanType: ['qrCode'],
        success: (res) => {
          this.handleScannedCode(res.result);
        },
        fail: () => {
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          });
        }
      });
    },
    async handleScannedCode(codeData) {
      try {
        console.log('📱 扫描到的数据:', codeData);

        // 提取预制码
        let precode = '';

        // 如果扫描的是URL，尝试提取预制码
        if (codeData.includes('http')) {
          // 从URL中提取预制码，例如：http://domain.com/pay/qrcode/ABC12345/
          const matches = codeData.match(/\/qrcode\/([A-Z0-9]+)\//);
          if (matches && matches[1]) {
            precode = matches[1];
          } else {
            // 尝试从URL参数中提取
            const urlParams = new URLSearchParams(codeData.split('?')[1] || '');
            precode = urlParams.get('precode') || urlParams.get('code') || '';
          }
        } else {
          // 如果直接是预制码字符串
          precode = codeData.trim();
        }

        if (!precode) {
          uni.showToast({
            title: '无法识别预制码',
            icon: 'none'
          });
          return;
        }

        console.log('🔍 提取的预制码:', precode);

        // 验证预制码是否有效
        uni.showLoading({ title: '验证中...' });

        const verifyResult = await verifyPreCode(precode);
        uni.hideLoading();

        console.log('📥 验证结果:', verifyResult);

        if (verifyResult && verifyResult.code === 0) {
          // 预制码有效，跳转到绑定页面
          this.openPreCodeBindModal(precode);
        } else {
          uni.showToast({
            title: verifyResult?.msg || '预制码无效或已被绑定',
            icon: 'none'
          });
        }

      } catch (error) {
        uni.hideLoading();
        console.error('❌ 处理扫描结果失败:', error);
        uni.showToast({
          title: '处理失败，请重试',
          icon: 'none'
        });
      }
    },
    enterCodeManually() {
      this.showManualInput = true;
    },
    async verifyCode() {
      if (!this.manualCodeInput.trim()) {
        uni.showToast({
          title: '请输入空码编号',
          icon: 'none'
        });
        return;
      }

      try {
        // 验证预制码
        uni.showLoading({ title: '验证中...' });

        const verifyResult = await verifyPreCode(this.manualCodeInput.trim());
        uni.hideLoading();

        console.log('📥 手动验证结果:', verifyResult);

        if (verifyResult && verifyResult.code === 0) {
          this.showBindCodeModal = false;
          this.openPreCodeBindModal(this.manualCodeInput.trim());
        } else {
          uni.showToast({
            title: verifyResult?.msg || '预制码无效或已被绑定',
            icon: 'none'
          });
        }

      } catch (error) {
        uni.hideLoading();
        console.error('❌ 验证预制码失败:', error);
        uni.showToast({
          title: '验证失败，请重试',
          icon: 'none'
        });
      }
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}/${month}/${day}`;
    },

    // 保存创建收款码
    async saveCreateQRCode() {
      try {
        if (!this.createForm.name.trim()) {
          uni.showToast({
            title: '请输入收款码名称',
            icon: 'none'
          });
          return;
        }

        console.log('💾 创建收款码:', this.createForm);

        const response = await createQRConfig(this.createForm);

        console.log('📥 创建响应:', response);

        if (response && response.code === 0) {
          this.showCreateModal = false;

          // 重新加载收款码列表
          await this.loadQRCodeList();

          uni.showToast({
            title: '创建成功',
            icon: 'success'
          });
        } else {
          throw new Error(response?.msg || '创建失败');
        }
      } catch (error) {
        console.error('❌ 创建收款码失败:', error);
        uni.showToast({
          title: error.message || '创建失败',
          icon: 'none'
        });
      }
    },

    // 保存编辑收款码
    async saveEditQRCode() {
      try {
        if (!this.editForm.name.trim()) {
          uni.showToast({
            title: '请输入收款码名称',
            icon: 'none'
          });
          return;
        }

        console.log('💾 编辑收款码:', this.editForm);

        // 确保传递config_id参数
        const editData = {
          ...this.editForm,
          config_id: this.editForm.id
        };

        const response = await editQRConfig(editData);

        console.log('📥 编辑响应:', response);

        if (response && response.code === 0) {
          this.showEditModal = false;

          // 重新加载收款码列表
          await this.loadQRCodeList();

          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });
        } else {
          throw new Error(response?.msg || '保存失败');
        }
      } catch (error) {
        console.error('❌ 编辑收款码失败:', error);
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        });
      }
    },

    // 取消创建
    cancelCreate() {
      this.showCreateModal = false;
    },

    // 取消编辑
    cancelEdit() {
      this.showEditModal = false;
    },

    // 获取选中员工名称
    getSelectedStaffName(staffId) {
      if (!staffId) {
        return '商户自己';
      }
      const staff = this.staffList.find(item => item.id === staffId);
      return staff ? staff.name : '未知员工';
    },

    // 创建收款码 - 员工选择相关
    showCreateStaffPicker() {
      this.showCreateStaffDropdown = true;
    },
    hideCreateStaffPicker() {
      this.showCreateStaffDropdown = false;
    },
    selectCreateStaff(staffId) {
      this.createForm.staff_id = staffId;
      this.showCreateStaffDropdown = false;
    },

    // 创建收款码 - 样式选择相关
    showCreateStylePicker() {
      this.showCreateStyleDropdown = true;
    },
    hideCreateStylePicker() {
      this.showCreateStyleDropdown = false;
    },
    selectCreateStyle(styleValue) {
      this.createForm.style = styleValue;
      this.showCreateStyleDropdown = false;
    },

    // 编辑收款码 - 员工选择相关
    showEditStaffPicker() {
      this.showEditStaffDropdown = true;
    },
    hideEditStaffPicker() {
      this.showEditStaffDropdown = false;
    },
    selectEditStaff(staffId) {
      this.editForm.staff_id = staffId;
      this.showEditStaffDropdown = false;
    },

    // 编辑收款码 - 样式选择相关
    showEditStylePicker() {
      this.showEditStyleDropdown = true;
    },
    hideEditStylePicker() {
      this.showEditStyleDropdown = false;
    },
    selectEditStyle(styleValue) {
      this.editForm.style = styleValue;
      this.showEditStyleDropdown = false;
    },

    // 预制码绑定相关方法
    openPreCodeBindModal(precode) {
      console.log('📱 显示预制码绑定弹窗:', precode);

      // 重置绑定表单
      this.preCodeBindForm = {
        precode: precode,
        name: `收款码-${precode}`,
        style: 'native',
        staff_id: null,
        description: `绑定预制码: ${precode}`
      };

      this.showPreCodeBindModal = true;
    },

    cancelPreCodeBind() {
      this.showPreCodeBindModal = false;
      this.showPreCodeStaffDropdown = false;
      this.showPreCodeStyleDropdown = false;
    },

    async confirmPreCodeBind() {
      try {
        if (!this.preCodeBindForm.name.trim()) {
          uni.showToast({
            title: '请输入收款码名称',
            icon: 'none'
          });
          return;
        }

        console.log('💾 绑定预制码:', this.preCodeBindForm);

        uni.showLoading({ title: '绑定中...' });

        const response = await bindPreCode(this.preCodeBindForm);

        uni.hideLoading();

        console.log('📥 绑定响应:', response);

        if (response && response.code === 0) {
          this.showPreCodeBindModal = false;

          // 重新加载收款码列表
          await this.loadQRCodeList();

          uni.showToast({
            title: '绑定成功',
            icon: 'success'
          });
        } else {
          throw new Error(response?.msg || '绑定失败');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('❌ 绑定预制码失败:', error);
        uni.showToast({
          title: error.message || '绑定失败',
          icon: 'none'
        });
      }
    },

    // 预制码绑定 - 员工选择相关
    showPreCodeStaffPicker() {
      this.showPreCodeStaffDropdown = true;
    },
    hidePreCodeStaffPicker() {
      this.showPreCodeStaffDropdown = false;
    },
    selectPreCodeStaff(staffId) {
      this.preCodeBindForm.staff_id = staffId;
      this.showPreCodeStaffDropdown = false;
    },

    // 预制码绑定 - 样式选择相关
    showPreCodeStylePicker() {
      this.showPreCodeStyleDropdown = true;
    },
    hidePreCodeStylePicker() {
      this.showPreCodeStyleDropdown = false;
    },
    selectPreCodeStyle(styleValue) {
      this.preCodeBindForm.style = styleValue;
      this.showPreCodeStyleDropdown = false;
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  font-family: 'Segoe UI', sans-serif;
  background-color: #f5f5f5;
}

.container {
  width: 100%;
  position: relative;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
}

/* 优雅现代导航栏样式 */
.elegant-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #5145F7 0%, #6366F1 50%, #8B5CF6 100%);
  box-shadow: 0 4rpx 20rpx rgba(81, 69, 247, 0.15);
}

.status-bar {
  width: 100%;
}

.navbar-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  min-height: 88rpx;
}

.navbar-title-section {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.page-title {
  font-size: 36rpx;
  color: #fff;
  font-weight: 600;
  margin-bottom: 4rpx;
  letter-spacing: 0.5rpx;
}

.page-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.action-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}

/* WebSocket状态指示器 */
.websocket-status-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.websocket-status-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.25);
}

.status-icon-wrapper {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.status-icon-wrapper.connected {
  background: rgba(52, 199, 89, 0.8); /* 绿色 - 已连接 */
  animation: pulse 2s infinite;
}

.status-icon-wrapper.connecting {
  background: rgba(255, 149, 0, 0.8); /* 橙色 - 连接中 */
  animation: spin 1s linear infinite;
}

.status-icon-wrapper.muted {
  background: rgba(142, 142, 147, 0.8); /* 灰色 - 静音 */
}

.status-icon {
  font-size: 24rpx;
  color: #fff;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.action-icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 32rpx;
  line-height: 1;
}

.action-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}

/* 导航栏占位 - 自适应各种设备 */
.navbar-placeholder {
  width: 100%;
  /* 针对iPhone 14 Pro Max等设备的特殊处理 */
  height: calc(var(--status-bar-height, 47px) + 44px);
  /* 使用安全区域，兼容刘海屏和动态岛 */
  height: calc(env(safe-area-inset-top, 47px) + 44px);
  /* 最小高度保证，适配iPhone 14 Pro Max */
  min-height: 91px;
  flex-shrink: 0;
}

/* 针对不同平台的适配 */
/* #ifdef H5 */
.navbar-placeholder {
  /* H5环境，针对iPhone 14 Pro Max的动态岛 */
  height: calc(env(safe-area-inset-top, 47px) + 44px);
  min-height: 91px;
}
/* #endif */

/* #ifdef MP */
.navbar-placeholder {
  height: calc(var(--status-bar-height, 47px) + 44px);
  min-height: 91px;
}
/* #endif */

/* #ifdef APP-PLUS */
.navbar-placeholder {
  height: calc(var(--status-bar-height, 47px) + 44px);
  min-height: 91px;
}
/* #endif */

.content {
  padding: 0 30rpx;
}

/* 商家信息卡片 */
.qrcode-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.merchant-name {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.merchant-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.qrcode-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;
}

.qrcode {
  width: 400rpx;
  height: 400rpx;
  border: 1px solid #eee;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.qrcode:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
}

.qrcode-placeholder {
  width: 350rpx;
  height: 350rpx;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  position: relative;
}

.qr-click-hint {
  position: absolute;
  bottom: 16rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  white-space: nowrap;
  opacity: 0.8;
}

.qrcode-image {
  width: 100%;
  height: 100%;
}

.save-image-btn {
  background-color: #5145F7;
  color: white;
  border-radius: 100rpx;
  padding: 20rpx 80rpx;
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.download-icon {
  margin-right: 10rpx;
}

/* 设置卡片 */
.settings-card {
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.settings-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.settings-list {
  border-radius: 8rpx;
  overflow: hidden;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1px solid #f2f2f2;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-left {
  display: flex;
  align-items: center;
}

.setting-icon {
  margin-right: 16rpx;
  font-size: 32rpx;
}

.setting-label {
  font-size: 28rpx;
  color: #333;
}

.setting-right {
  display: flex;
  align-items: center;
}

.setting-value {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.arrow {
  color: #ccc;
  font-size: 28rpx;
}

/* 保存按钮 */
.save-button {
  margin-top: 40rpx;
  background-color: #5145F7;
  color: white;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.save-icon {
  margin-right: 10rpx;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
}

.staff-modal {
  height: 65vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  position: relative;
  height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  border-bottom: 1rpx solid #eee;
}

.close-icon {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 40rpx;
  color: #999;
}

.modal-body {
  padding: 30rpx;
}

.name-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.modal-footer button {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
  border-radius: 0;
  border: none;
}

.cancel-btn {
  color: #666;
  background-color: #f5f5f5;
}

.confirm-btn {
  color: white;
  background-color: #5145F7;
}

/* 员工列表样式 */
.staff-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.staff-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f2f2f2;
}

.staff-item-left {
  display: flex;
  align-items: center;
}

.staff-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #5145F7;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  margin-right: 15rpx;
}

.staff-detail {
  display: flex;
  flex-direction: column;
}

.staff-name {
  font-size: 28rpx;
  color: #333;
}

.staff-role {
  font-size: 24rpx;
  color: #999;
}

.staff-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 1rpx solid #ddd;
  border-radius: 50%;
}

.staff-checkbox.checked {
  background-color: #5145F7;
  border-color: #5145F7;
  position: relative;
}

.staff-checkbox.checked::after {
  content: "";
  position: absolute;
  top: 25%;
  left: 25%;
  width: 50%;
  height: 25%;
  border-left: 4rpx solid white;
  border-bottom: 4rpx solid white;
  transform: rotate(-45deg);
}

/* 背景颜色类 */
.bg-purple {
  background-color: #5145F7;
}

.bg-green {
  background-color: #32CD32;
}

.bg-blue {
  background-color: #3B82F6;
}

/* 表单样式 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.form-select {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  background-color: #fff;
}

.select-text {
  font-size: 28rpx;
  color: #333;
}

/* 已生成收款码区域 */
.generated-codes-card {
  margin-top: 20rpx;
  padding: 30rpx 30rpx 20rpx 30rpx;
}

.codes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.code-actions-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.bind-code-btn {
  color: #5145F7;
  font-size: 28rpx;
}

.create-code-btn {
  color: #5145F7;
  font-size: 28rpx;
}

.code-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.code-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-radius: 8rpx;
}

.code-item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.qr-icon-box {
  width: 45rpx;
  height: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15rpx;
}

.code-info {
  display: flex;
  flex-direction: column;
}

.code-name {
  font-size: 28rpx;
  color: #333;
}

.code-date {
  font-size: 24rpx;
  color: #666;
  margin-top: 5rpx;
}

.code-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  width: 45rpx;
  height: 45rpx;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

/* 已生成收款码背景颜色 */
.bg-blue-light {
  background-color: #E9F1FF;
}

.bg-green-light {
  background-color: #E4FFEF;
}

.bg-purple-light {
  background-color: #F0E5FF;
}

/* 空码条目样式 */
.empty-code-entry {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  background-color: white;
  border: 1px dashed #5145F7;
  border-radius: 8rpx;
  margin-top: 15rpx;
}

.empty-code-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 8rpx;
  background-color: #5145F7;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15rpx;
}

.empty-code-text {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.empty-code-desc {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 绑定空码弹窗样式 */
.bind-code-modal {
  height: auto;
  max-height: 80vh;
}

.bind-code-options {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.bind-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.bind-option-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15rpx;
}

.scan-icon {
  background-color: #5145F7;
}

.manual-icon {
  background-color: #4CD964;
}

.bind-option-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.bind-option-desc {
  font-size: 22rpx;
  color: #999;
  text-align: center;
}

.code-manual-input {
  display: flex;
  margin-bottom: 30rpx;
}

.code-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx 0 0 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.verify-btn {
  width: 160rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #5145F7;
  color: white;
  font-size: 28rpx;
  text-align: center;
  border-radius: 0 8rpx 8rpx 0;
  border: none;
}

.code-tips {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
}

.tips-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.tips-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* 样式选择弹窗样式 */
.style-modal {
  max-height: 80vh;
}

.style-list {
  max-height: 60vh;
  overflow-y: auto;
}

.style-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.style-item:last-child {
  border-bottom: none;
}

.style-item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.style-preview {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-right: 24rpx;
}

.style-icon {
  font-size: 32rpx;
}

.style-detail {
  display: flex;
  flex-direction: column;
}

.style-name {
  font-size: 32rpx;
  color: #333;
}

.style-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

.style-checkbox.checked {
  border-color: #5145F7;
  background-color: #5145F7;
}

.style-checkbox.checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24rpx;
}

/* 加载状态样式 */
.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
  font-size: 28rpx;
}

/* 设置图标样式 */
.style-icon {
  margin-right: 12rpx;
}

/* 下拉选择样式 */
.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dropdown-content {
  background-color: white;
  border-radius: 16rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.dropdown-header {
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.dropdown-list {
  max-height: 60vh;
  overflow-y: auto;
}

.dropdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1px solid #f8f8f8;
  cursor: pointer;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f8f9ff;
}

.dropdown-item.active {
  background-color: #f0f2ff;
  color: #5145F7;
}

.dropdown-item-content {
  display: flex;
  align-items: center;
}

.dropdown-item text {
  font-size: 28rpx;
}

.check-icon {
  color: #5145F7;
  font-size: 32rpx;
  font-weight: bold;
}

.form-select {
  cursor: pointer;
}

.form-select:hover {
  border-color: #5145F7;
}

/* 只读输入框样式 */
.readonly-input {
  background-color: #f5f5f5 !important;
  color: #999 !important;
  cursor: not-allowed;
}

/* 预制码绑定弹窗特殊样式 */
.precode-bind-modal {
  max-height: 85vh;
}

.precode-bind-modal .modal-body {
  max-height: 60vh;
  overflow-y: auto;
}

/* 🎨 样式化二维码弹窗样式 */
.styled-qr-modal .modal-content {
  max-width: 90vw;
  max-height: 85vh;
  width: 650rpx;
}

.styled-qr-content {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.styled-qr-body {
  padding: 32rpx;
  text-align: center;
}

.styled-qr-container {
  margin-bottom: 32rpx;
}

.styled-qr-wrapper {
  width: 500rpx;
  height: 500rpx;
  margin: 0 auto 16rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}

.styled-qr-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 28rpx;
  color: #999;
}

.styled-qr-image {
  width: 100%;
  height: 100%;
}

.styled-qr-tip {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 24rpx;
}

.style-info {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.style-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.style-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.styled-qr-footer {
  display: flex;
  gap: 20rpx;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
}

.save-styled-btn,
.share-styled-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.save-styled-btn {
  background: #5145F7;
  color: #fff;
}

.save-styled-btn:active {
  background: #4338CA;
  transform: scale(0.98);
}

.share-styled-btn {
  background: #fff;
  color: #5145F7;
  border: 2rpx solid #5145F7;
}

.share-styled-btn:active {
  background: #f0f0ff;
  transform: scale(0.98);
}

/* 🎯 查看收款码弹窗样式 */
.view-qr-modal .modal-content {
  max-width: 90vw;
  max-height: 90vh;
  width: 680rpx;
}

.view-qr-content {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.view-qr-body {
  padding: 32rpx;
  max-height: 90vh;
  overflow-y: auto;
}

/* 收款码信息区域 */
.qr-info-section {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.qr-info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.qr-info-item:last-child {
  margin-bottom: 0;
}

.qr-info-label {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.qr-info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 样式化二维码显示区域 */
.view-qr-container {
  text-align: center;
}

.view-qr-code {
  width: 500rpx;
  height: 500rpx;
  margin: 0 auto 16rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 28rpx;
  color: #999;
}

.view-qr-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 小程序样式化二维码容器 - 按照收款码通用比例 10:15 */
.view-qr-body .styled-qr-container {
  position: relative;
  width: 500rpx;
  height: 750rpx;
  margin: 0 auto;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  overflow: visible;
  border-radius: 16rpx;
  background: #f5f5f5;
}

.styled-qr-background {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center top;
}

.styled-qr-overlay {
  position: absolute;
  object-fit: contain;
  z-index: 2;
}

.qr-tip {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 24rpx;
}

/* 查看弹窗底部按钮 */
.view-qr-footer {
  display: flex;
  justify-content: center;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
}

.save-qr-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 24rpx 48rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  background: #5145F7;
  color: #fff;
  transition: all 0.3s ease;
  min-width: 200rpx;
}

.save-qr-btn:active {
  background: #4338CA;
  transform: scale(0.98);
}

.save-icon {
  font-size: 30rpx;
}

/* 查看按钮样式 */
.view-btn {
  background: rgba(81, 69, 247, 0.1);
  border-radius: 8rpx;
  padding: 8rpx;
  margin-right: 8rpx;
  transition: all 0.3s ease;
}

.view-btn:active {
  background: rgba(81, 69, 247, 0.2);
  transform: scale(0.95);
}
</style>




