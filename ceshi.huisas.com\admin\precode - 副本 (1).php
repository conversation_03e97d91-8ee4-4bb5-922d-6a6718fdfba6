<?php
/**
 * 预制收款码管理
 * 文件路径: epay_release_99009/admin/precode.php
 */
include("../includes/common.php");
$title='预制收款码管理';
include './head.php';
if($islogin==1){}else exit("<script language='javascript'>window.location.href='./login.php';</script>");

// 检查并创建预制码表
$create_table_sql = "CREATE TABLE IF NOT EXISTS `{$dbconfig['dbqz']}_precode` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `code` varchar(50) NOT NULL COMMENT '预制码',
    `uid` int(11) DEFAULT NULL COMMENT '绑定的商户ID',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0未绑定 1已绑定',
    `addtime` datetime DEFAULT NULL COMMENT '生成时间',
    `bindtime` datetime DEFAULT NULL COMMENT '绑定时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `code` (`code`),
    KEY `uid` (`uid`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预制收款码表';";

try {
    $DB->exec($create_table_sql);
} catch(Exception $e) {
    // 表可能已存在，忽略错误
}

// 处理POST请求
if($_POST['action'] == 'generate') {
    $count = intval($_POST['count']);
    if($count < 1 || $count > 100) {
        $msg = '<div class="alert alert-danger">生成数量必须在1-100之间</div>';
    } else {
        $success_count = 0;
        for($i = 0; $i < $count; $i++) {
            $code = generatePreCode();
            $result = $DB->exec("INSERT INTO `{$dbconfig['dbqz']}_precode` (`code`, `addtime`) VALUES (:code, NOW())", [':code' => $code]);
            if($result) $success_count++;
        }
        $msg = '<div class="alert alert-success">成功生成 '.$success_count.' 个预制码</div>';
    }
} elseif($_POST['action'] == 'delete') {
    $id = intval($_POST['id']);
    $result = $DB->exec("DELETE FROM `{$dbconfig['dbqz']}_precode` WHERE `id`=:id AND `status`=0", [':id' => $id]);
    if($result) {
        $msg = '<div class="alert alert-success">删除成功</div>';
    } else {
        $msg = '<div class="alert alert-danger">删除失败，可能该预制码已被绑定</div>';
    }
} elseif($_POST['action'] == 'unbind') {
    $id = intval($_POST['id']);
    $result = $DB->exec("UPDATE `{$dbconfig['dbqz']}_precode` SET `uid`=NULL, `status`=0, `bindtime`=NULL WHERE `id`=:id", [':id' => $id]);
    if($result) {
        $msg = '<div class="alert alert-success">解绑成功</div>';
    } else {
        $msg = '<div class="alert alert-danger">解绑失败</div>';
    }
} elseif($_POST['action'] == 'batch_delete') {
    $result = $DB->exec("DELETE FROM `{$dbconfig['dbqz']}_precode` WHERE `status`=0");
    $msg = '<div class="alert alert-success">批量删除未绑定预制码成功，共删除 '.$result.' 个</div>';
}

// 分页参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// 搜索参数
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? intval($_GET['status']) : -1;

// 构建查询条件
$where_conditions = [];
$params = [];

if($search) {
    $where_conditions[] = "(`code` LIKE :search OR `uid` LIKE :search)";
    $params[':search'] = '%'.$search.'%';
}

if($status_filter >= 0) {
    $where_conditions[] = "`status` = :status";
    $params[':status'] = $status_filter;
}

$where_sql = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// 获取总数
$total = $DB->getColumn("SELECT COUNT(*) FROM `{$dbconfig['dbqz']}_precode` $where_sql", $params);
$total_pages = ceil($total / $limit);

// 获取预制码列表
$list = $DB->getAll("SELECT * FROM `{$dbconfig['dbqz']}_precode` $where_sql ORDER BY `id` DESC LIMIT $offset, $limit", $params);

// 统计信息
$stats = $DB->getRow("SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN status=0 THEN 1 ELSE 0 END) as unbound,
    SUM(CASE WHEN status=1 THEN 1 ELSE 0 END) as bound
    FROM `{$dbconfig['dbqz']}_precode`");
?>

<div class="container" style="padding-top:70px;">
    <div class="row">
        <div class="col-md-12">
            <h3>预制收款码管理</h3>
            
            <?php if(isset($msg)) echo $msg; ?>
            
            <!-- 统计信息 -->
            <div class="row" style="margin-bottom:20px;">
                <div class="col-md-3">
                    <div class="panel panel-primary">
                        <div class="panel-body text-center">
                            <h4><?php echo $stats['total']; ?></h4>
                            <p>总预制码数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="panel panel-success">
                        <div class="panel-body text-center">
                            <h4><?php echo $stats['unbound']; ?></h4>
                            <p>未绑定</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="panel panel-warning">
                        <div class="panel-body text-center">
                            <h4><?php echo $stats['bound']; ?></h4>
                            <p>已绑定</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="panel panel-info">
                        <div class="panel-body text-center">
                            <h4><?php echo round($stats['bound']/$stats['total']*100, 1); ?>%</h4>
                            <p>绑定率</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作面板 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4>批量生成预制码</h4>
                </div>
                <div class="panel-body">
                    <form method="post" class="form-inline">
                        <input type="hidden" name="action" value="generate">
                        <div class="form-group">
                            <label>生成数量：</label>
                            <input type="number" name="count" class="form-control" min="1" max="100" value="10" required>
                        </div>
                        <button type="submit" class="btn btn-primary">生成预制码</button>
                        <button type="button" class="btn btn-danger" onclick="batchDelete()">批量删除未绑定</button>
                    </form>
                </div>
            </div>
            
            <!-- 搜索面板 -->
            <div class="panel panel-default">
                <div class="panel-body">
                    <form method="get" class="form-inline">
                        <div class="form-group">
                            <input type="text" name="search" class="form-control" placeholder="搜索预制码或商户ID" value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="form-group">
                            <select name="status" class="form-control">
                                <option value="-1">全部状态</option>
                                <option value="0" <?php echo $status_filter==0?'selected':''; ?>>未绑定</option>
                                <option value="1" <?php echo $status_filter==1?'selected':''; ?>>已绑定</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-default">搜索</button>
                        <a href="?" class="btn btn-default">重置</a>
                    </form>
                </div>
            </div>
            
            <!-- 预制码列表 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4>预制码列表 (共 <?php echo $total; ?> 个)</h4>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>预制码</th>
                                <th>绑定商户</th>
                                <th>状态</th>
                                <th>生成时间</th>
                                <th>绑定时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($list as $row): ?>
                            <tr>
                                <td><?php echo $row['id']; ?></td>
                                <td><code><?php echo $row['code']; ?></code></td>
                                <td><?php echo $row['uid'] ? $row['uid'] : '-'; ?></td>
                                <td>
                                    <?php if($row['status'] == 0): ?>
                                        <span class="label label-success">未绑定</span>
                                    <?php else: ?>
                                        <span class="label label-warning">已绑定</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $row['addtime']; ?></td>
                                <td><?php echo $row['bindtime'] ? $row['bindtime'] : '-'; ?></td>
                                <td>
                                    <?php if($row['status'] == 0): ?>
                                        <button class="btn btn-sm btn-danger" onclick="deleteCode(<?php echo $row['id']; ?>)">删除</button>
                                    <?php else: ?>
                                        <button class="btn btn-sm btn-warning" onclick="unbindCode(<?php echo $row['id']; ?>)">解绑</button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 分页 -->
            <?php if($total_pages > 1): ?>
            <nav>
                <ul class="pagination">
                    <?php for($i = 1; $i <= $total_pages; $i++): ?>
                    <li class="<?php echo $i==$page?'active':''; ?>">
                        <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status_filter; ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor; ?>
                </ul>
            </nav>
            <?php endif; ?>
            
        </div>
    </div>
</div>

<script>
function deleteCode(id) {
    if(confirm('确定要删除这个预制码吗？')) {
        var form = document.createElement('form');
        form.method = 'post';
        form.innerHTML = '<input type="hidden" name="action" value="delete"><input type="hidden" name="id" value="' + id + '">';
        document.body.appendChild(form);
        form.submit();
    }
}

function unbindCode(id) {
    if(confirm('确定要解绑这个预制码吗？解绑后可以重新绑定给其他商户。')) {
        var form = document.createElement('form');
        form.method = 'post';
        form.innerHTML = '<input type="hidden" name="action" value="unbind"><input type="hidden" name="id" value="' + id + '">';
        document.body.appendChild(form);
        form.submit();
    }
}

function batchDelete() {
    if(confirm('确定要批量删除所有未绑定的预制码吗？此操作不可恢复！')) {
        var form = document.createElement('form');
        form.method = 'post';
        form.innerHTML = '<input type="hidden" name="action" value="batch_delete">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

</body>
</html>
