// api/pay.js - 支付相关API
import { get, post } from '@/utils/request';
import { signParams, generateNonceStr } from '@/utils/sign';
import config from '@/config/index';

/**
 * 查询账户余额
 * @returns {Promise} 请求结果，包含可用余额和手续费率
 */
export function getBalance() {
  const merchantId = uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('merchantKey') || config.merchant.key;
  
  const timestamp = Math.floor(Date.now() / 1000).toString();
  
  // 构建参数
  const params = {
    pid: merchantId,
    timestamp: timestamp,
    sign_type: 'MD5'
  };
  
  // 添加签名
  params.sign = signParams(params, merchantKey);
  
  // 使用余额查询API
  return post('/api/transfer/balance', params);
}

/**
 * 统一下单接口
 * @param {Object} data - 支付参数
 * @returns {Promise} 支付结果，包含不同类型的支付信息
 */
export function createOrder(data) {
  const merchantId = uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('merchantKey') || config.merchant.key;
  
  const timestamp = Math.floor(Date.now() / 1000).toString();
  
  // 构建参数
  const params = {
    pid: merchantId,
    method: data.method || 'web', // 接口类型: web/jump/jsapi/app/scan/applet
    device: data.device || 'pc', // 设备类型，method为web时需要
    type: data.payType || '', // 支付方式
    out_trade_no: data.orderId,
    notify_url: data.notifyUrl || config.notifyUrl,
    return_url: data.returnUrl || config.returnUrl,
    name: data.productName,
    money: data.amount,
    clientip: data.clientIp || '127.0.0.1',
    param: data.param || '',
    timestamp: timestamp
  };
  
  // 根据不同的支付方式，可能需要添加不同的参数
  if (data.method === 'scan' && data.authCode) {
    params.auth_code = data.authCode; // 被扫支付需要授权码
  }
  
  if (data.method === 'jsapi') {
    params.sub_openid = data.openId; // JSAPI支付需要openid
    params.sub_appid = data.appId; // JSAPI支付需要appid
  }
  
  // 添加签名
  // 注意：文档中说明默认为RSA签名，但我们目前使用MD5签名
  // 当您有RSA私钥时，可以切换到RSA签名
  params.sign_type = 'MD5'; // 使用MD5签名
  params.sign = signParams(params, merchantKey);
  
  // 调用统一下单接口
  return post('/api/pay/create', params);
}

/**
 * 创建支付链接（兼容原先的接口，内部调用统一下单）
 * @param {Object} data - 支付参数
 * @returns {String} 支付链接
 */
export function createPayUrl(data) {
  const merchantId = uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('merchantKey') || config.merchant.key;
  
  const params = {
    pid: merchantId,
    type: data.payType || '', // 支付方式，可空
    out_trade_no: data.orderId,
    notify_url: data.notifyUrl || config.notifyUrl,
    return_url: data.returnUrl || config.returnUrl,
    name: data.productName,
    money: data.amount,
    sitename: data.siteName || '商家收银台',
    param: data.param || '',
  };
  
  // 添加签名
  params.sign = signParams(params, merchantKey);
  params.sign_type = 'MD5';
  
  // 构建URL
  let url = config.baseUrl + '/submit.php?';
  
  Object.keys(params).forEach(key => {
    url += `${key}=${encodeURIComponent(params[key])}&`;
  });
  
  return url.slice(0, -1); // 去掉最后一个&
}

/**
 * 申请退款
 * @param {String} outTradeNo - 商户订单号
 * @param {Number} amount - 退款金额
 * @returns {Promise} 请求结果
 */
export function refundOrder(outTradeNo, amount) {
  const merchantId = uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('merchantKey') || config.merchant.key;
  
  const params = {
    pid: merchantId,
    key: merchantKey,
    out_trade_no: outTradeNo,
    money: amount
  };
  
  return post('/api.php?act=refund', params);
}

/**
 * 生成收款二维码
 * @param {Object} data - 收款参数
 * @returns {Promise} 二维码链接或其他支付信息
 */
export function generateQrCode(data) {
  // 调用统一下单接口，指定method为web, device为pc
  const orderData = {
    ...data,
    method: 'web',
    device: 'pc'
  };
  
  return createOrder(orderData).then(result => {
    if (result.code === 0 && result.pay_type === 'qrcode') {
      return result.pay_info;
    } else if (result.code === 0) {
      return result; // 返回完整的响应，由调用方处理不同的支付类型
    } else {
      throw new Error(result.msg || '生成二维码失败');
    }
  });
}

/**
 * 获取支付通道列表
 * @returns {Promise} 请求结果，包含支付方式列表
 */
export function getPayChannels() {
  const merchantId = uni.getStorageSync('merchantId') || config.merchant.id;
  const merchantKey = uni.getStorageSync('merchantKey') || config.merchant.key;
  
  const timestamp = Math.floor(Date.now() / 1000).toString();
  
  // 构建参数
  const params = {
    pid: merchantId,
    timestamp: timestamp,
    sign_type: 'MD5'
  };
  
  // 添加签名
  params.sign = signParams(params, merchantKey);
  
  // 请求支付通道列表
  return post('/api/pay/channels', params);
} 