<?php
/**
 * 测试预制码生成功能
 */
include("includes/common.php");

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>预制码生成测试</h2>";

// 检查数据库连接
echo "<h3>1. 数据库连接测试</h3>";
try {
    $test = $DB->getColumn("SELECT 1");
    echo "✅ 数据库连接正常<br>";
} catch(Exception $e) {
    echo "❌ 数据库连接失败：" . $e->getMessage() . "<br>";
    exit;
}

// 检查表是否存在
echo "<h3>2. 数据表检查</h3>";
try {
    $tables = $DB->getColumn("SHOW TABLES LIKE '{$dbconfig['dbqz']}_precode'");
    if($tables) {
        echo "✅ 预制码表已存在<br>";
    } else {
        echo "⚠️ 预制码表不存在，正在创建...<br>";
        $DB->exec("CREATE TABLE IF NOT EXISTS `{$dbconfig['dbqz']}_precode` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `code` varchar(50) NOT NULL COMMENT '预制码',
            `qr_url` text COMMENT '完整的二维码URL',
            `uid` int(11) DEFAULT NULL COMMENT '绑定的商户ID',
            `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0未绑定 1已绑定',
            `addtime` datetime DEFAULT NULL COMMENT '生成时间',
            `bindtime` datetime DEFAULT NULL COMMENT '绑定时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `code` (`code`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预制收款码';");
        echo "✅ 预制码表创建成功<br>";
    }
} catch(Exception $e) {
    echo "❌ 表检查失败：" . $e->getMessage() . "<br>";
}

// 检查generatePreCode函数
echo "<h3>3. 生成函数测试</h3>";
if(function_exists('generatePreCode')) {
    echo "✅ generatePreCode函数存在<br>";
    
    // 测试生成预制码
    try {
        $code = generatePreCode();
        if($code) {
            echo "✅ 成功生成预制码：<strong>$code</strong><br>";
        } else {
            echo "❌ 生成预制码失败<br>";
        }
    } catch(Exception $e) {
        echo "❌ 生成预制码异常：" . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ generatePreCode函数不存在<br>";
}

// 测试数据库插入
echo "<h3>4. 数据库插入测试</h3>";
try {
    $test_code = 'TEST' . date('His');
    $qr_url = $siteurl . 'paypage/precode/' . $test_code;
    
    $result = $DB->exec("INSERT INTO `{$dbconfig['dbqz']}_precode` (`code`, `qr_url`, `addtime`) VALUES (?, ?, NOW())", [$test_code, $qr_url]);
    
    if($result) {
        echo "✅ 数据库插入成功，测试码：<strong>$test_code</strong><br>";
        
        // 清理测试数据
        $DB->exec("DELETE FROM `{$dbconfig['dbqz']}_precode` WHERE `code`=?", [$test_code]);
        echo "✅ 测试数据已清理<br>";
    } else {
        echo "❌ 数据库插入失败<br>";
    }
} catch(Exception $e) {
    echo "❌ 数据库插入异常：" . $e->getMessage() . "<br>";
}

// 测试完整流程
echo "<h3>5. 完整流程测试</h3>";
try {
    $success = 0;
    $codes = [];
    
    for($i = 0; $i < 3; $i++) {
        $code = generatePreCode();
        if($code) {
            $qr_url = $siteurl . 'paypage/precode/' . $code;
            $result = $DB->exec("INSERT INTO `{$dbconfig['dbqz']}_precode` (`code`, `qr_url`, `addtime`) VALUES (?, ?, NOW())", [$code, $qr_url]);
            if($result) {
                $success++;
                $codes[] = $code;
            }
        }
    }
    
    if($success > 0) {
        echo "✅ 完整流程测试成功，生成了 $success 个预制码：<br>";
        foreach($codes as $code) {
            echo "- <strong>$code</strong><br>";
        }
        
        // 清理测试数据
        foreach($codes as $code) {
            $DB->exec("DELETE FROM `{$dbconfig['dbqz']}_precode` WHERE `code`=?", [$code]);
        }
        echo "✅ 测试数据已清理<br>";
    } else {
        echo "❌ 完整流程测试失败<br>";
    }
} catch(Exception $e) {
    echo "❌ 完整流程测试异常：" . $e->getMessage() . "<br>";
}

echo "<h3>6. 系统信息</h3>";
echo "PHP版本：" . PHP_VERSION . "<br>";
echo "数据库前缀：{$dbconfig['dbqz']}<br>";
echo "站点URL：$siteurl<br>";

echo "<hr>";
echo "<p><a href='admin/admin_precode_simple_working.php'>访问预制码管理页面</a></p>";
?>
