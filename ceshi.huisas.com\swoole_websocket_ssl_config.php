<?php
/**
 * Swoole WebSocket SSL配置文件
 * 用于支持小程序WSS连接
 */

// SSL证书配置
$sslConfig = [
    // 是否启用SSL
    'ssl_enabled' => true,
    
    // SSL证书文件路径（需要根据实际情况修改）
    'ssl_cert_file' => '/www/server/panel/vhost/cert/ceshi.huisas.com/fullchain.pem',
    'ssl_key_file' => '/www/server/panel/vhost/cert/ceshi.huisas.com/privkey.pem',
    
    // SSL配置选项
    'ssl_verify_peer' => false,
    'ssl_allow_self_signed' => true,
    'ssl_protocols' => SWOOLE_SSL_TLSv1_2 | SWOOLE_SSL_TLSv1_3,
];

// WebSocket服务器配置
$serverConfig = [
    'host' => '0.0.0.0',
    'port' => 8080,
    'worker_num' => 2,
    'heartbeat_interval' => 30,
    'max_connections' => 1000,
    'app_key' => 'payment_websocket_2024',
    
    // SSL相关配置
    'ssl_cert_file' => $sslConfig['ssl_cert_file'],
    'ssl_key_file' => $sslConfig['ssl_key_file'],
    'open_ssl' => $sslConfig['ssl_enabled'],
    'ssl_protocols' => $sslConfig['ssl_protocols'],
];

/**
 * 检查SSL证书文件是否存在
 */
function checkSSLCertificates($config) {
    $errors = [];
    
    if (!file_exists($config['ssl_cert_file'])) {
        $errors[] = "SSL证书文件不存在: " . $config['ssl_cert_file'];
    }
    
    if (!file_exists($config['ssl_key_file'])) {
        $errors[] = "SSL私钥文件不存在: " . $config['ssl_key_file'];
    }
    
    return $errors;
}

/**
 * 获取SSL配置
 */
function getSSLConfig() {
    global $sslConfig;
    return $sslConfig;
}

/**
 * 获取服务器配置
 */
function getServerConfig() {
    global $serverConfig;
    
    // 检查SSL证书
    if ($serverConfig['open_ssl']) {
        $errors = checkSSLCertificates($serverConfig);
        if (!empty($errors)) {
            echo "❌ SSL配置错误:\n";
            foreach ($errors as $error) {
                echo "   - $error\n";
            }
            echo "\n💡 解决方案:\n";
            echo "   1. 确保SSL证书已正确安装\n";
            echo "   2. 检查证书文件路径是否正确\n";
            echo "   3. 如果暂时不需要SSL，可以设置 'open_ssl' => false\n\n";
            
            // 暂时禁用SSL
            $serverConfig['open_ssl'] = false;
            echo "⚠️  已暂时禁用SSL，使用WS协议\n";
        }
    }
    
    return $serverConfig;
}

/**
 * 显示配置信息
 */
function displayConfig() {
    $config = getServerConfig();
    
    echo "🔧 WebSocket服务器配置:\n";
    echo "==========================================\n";
    echo "监听地址: " . $config['host'] . ":" . $config['port'] . "\n";
    echo "SSL状态: " . ($config['open_ssl'] ? "✅ 启用 (WSS)" : "❌ 禁用 (WS)") . "\n";
    
    if ($config['open_ssl']) {
        echo "证书文件: " . $config['ssl_cert_file'] . "\n";
        echo "私钥文件: " . $config['ssl_key_file'] . "\n";
        echo "连接协议: wss://" . $config['host'] . ":" . $config['port'] . "\n";
    } else {
        echo "连接协议: ws://" . $config['host'] . ":" . $config['port'] . "\n";
    }
    
    echo "工作进程: " . $config['worker_num'] . "\n";
    echo "心跳间隔: " . $config['heartbeat_interval'] . "秒\n";
    echo "最大连接: " . $config['max_connections'] . "\n";
    echo "==========================================\n";
}

// 如果直接运行此文件，显示配置信息
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    displayConfig();
}
