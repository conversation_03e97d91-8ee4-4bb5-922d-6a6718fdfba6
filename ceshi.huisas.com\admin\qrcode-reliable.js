/**
 * 可靠的二维码生成器
 * 基于成熟的QRCode.js库，确保扫描兼容性
 */

// QRCode.js - 经过验证的二维码生成库
var QRCode = function(typeNumber, errorCorrectionLevel) {
    this.typeNumber = typeNumber;
    this.errorCorrectionLevel = errorCorrectionLevel;
    this.modules = null;
    this.moduleCount = 0;
    this.dataCache = null;
    this.dataList = new Array();
};

QRCode.prototype = {
    addData: function(data) {
        var newData = new QRCode8bitByte(data);
        this.dataList.push(newData);
        this.dataCache = null;
    },

    isDark: function(row, col) {
        if (row < 0 || this.moduleCount <= row || col < 0 || this.moduleCount <= col) {
            throw new Error(row + "," + col);
        }
        return this.modules[row][col];
    },

    getModuleCount: function() {
        return this.moduleCount;
    },

    make: function() {
        this.makeImpl(false, this.getBestMaskPattern());
    },

    makeImpl: function(test, maskPattern) {
        this.moduleCount = this.typeNumber * 4 + 17;
        this.modules = new Array(this.moduleCount);

        for (var row = 0; row < this.moduleCount; row++) {
            this.modules[row] = new Array(this.moduleCount);
            for (var col = 0; col < this.moduleCount; col++) {
                this.modules[row][col] = null;
            }
        }

        this.setupPositionProbePattern(0, 0);
        this.setupPositionProbePattern(this.moduleCount - 7, 0);
        this.setupPositionProbePattern(0, this.moduleCount - 7);
        this.setupPositionAdjustPattern();
        this.setupTimingPattern();
        this.setupTypeInfo(test, maskPattern);

        if (this.typeNumber >= 7) {
            this.setupTypeNumber(test);
        }

        if (this.dataCache == null) {
            this.dataCache = QRCode.createData(this.typeNumber, this.errorCorrectionLevel, this.dataList);
        }

        this.mapData(this.dataCache, maskPattern);
    },

    setupPositionProbePattern: function(row, col) {
        for (var r = -1; r <= 7; r++) {
            if (row + r <= -1 || this.moduleCount <= row + r) continue;

            for (var c = -1; c <= 7; c++) {
                if (col + c <= -1 || this.moduleCount <= col + c) continue;

                if ((0 <= r && r <= 6 && (c == 0 || c == 6)) ||
                    (0 <= c && c <= 6 && (r == 0 || r == 6)) ||
                    (2 <= r && r <= 4 && 2 <= c && c <= 4)) {
                    this.modules[row + r][col + c] = true;
                } else {
                    this.modules[row + r][col + c] = false;
                }
            }
        }
    },

    getBestMaskPattern: function() {
        var minLostPoint = 0;
        var pattern = 0;

        for (var i = 0; i < 8; i++) {
            this.makeImpl(true, i);
            var lostPoint = QRCode.QRUtil.getLostPoint(this);

            if (i == 0 || minLostPoint > lostPoint) {
                minLostPoint = lostPoint;
                pattern = i;
            }
        }

        return pattern;
    },

    setupTimingPattern: function() {
        for (var r = 8; r < this.moduleCount - 8; r++) {
            if (this.modules[r][6] != null) {
                continue;
            }
            this.modules[r][6] = (r % 2 == 0);
        }

        for (var c = 8; c < this.moduleCount - 8; c++) {
            if (this.modules[6][c] != null) {
                continue;
            }
            this.modules[6][c] = (c % 2 == 0);
        }
    },

    setupPositionAdjustPattern: function() {
        var pos = QRCode.QRUtil.getPatternPosition(this.typeNumber);

        for (var i = 0; i < pos.length; i++) {
            for (var j = 0; j < pos.length; j++) {
                var row = pos[i];
                var col = pos[j];

                if (this.modules[row][col] != null) {
                    continue;
                }

                for (var r = -2; r <= 2; r++) {
                    for (var c = -2; c <= 2; c++) {
                        if (r == -2 || r == 2 || c == -2 || c == 2 || (r == 0 && c == 0)) {
                            this.modules[row + r][col + c] = true;
                        } else {
                            this.modules[row + r][col + c] = false;
                        }
                    }
                }
            }
        }
    },

    setupTypeNumber: function(test) {
        var bits = QRCode.QRUtil.getBCHTypeNumber(this.typeNumber);

        for (var i = 0; i < 18; i++) {
            var mod = (!test && ((bits >> i) & 1) == 1);
            this.modules[Math.floor(i / 3)][i % 3 + this.moduleCount - 8 - 3] = mod;
        }

        for (var i = 0; i < 18; i++) {
            var mod = (!test && ((bits >> i) & 1) == 1);
            this.modules[i % 3 + this.moduleCount - 8 - 3][Math.floor(i / 3)] = mod;
        }
    },

    setupTypeInfo: function(test, maskPattern) {
        var data = (this.errorCorrectionLevel << 3) | maskPattern;
        var bits = QRCode.QRUtil.getBCHTypeInfo(data);

        for (var i = 0; i < 15; i++) {
            var mod = (!test && ((bits >> i) & 1) == 1);

            if (i < 6) {
                this.modules[i][8] = mod;
            } else if (i < 8) {
                this.modules[i + 1][8] = mod;
            } else {
                this.modules[this.moduleCount - 15 + i][8] = mod;
            }
        }

        for (var i = 0; i < 15; i++) {
            var mod = (!test && ((bits >> i) & 1) == 1);

            if (i < 8) {
                this.modules[8][this.moduleCount - i - 1] = mod;
            } else if (i < 9) {
                this.modules[8][15 - i - 1 + 1] = mod;
            } else {
                this.modules[8][15 - i - 1] = mod;
            }
        }

        this.modules[this.moduleCount - 8][8] = (!test);
    },

    mapData: function(data, maskPattern) {
        var inc = -1;
        var row = this.moduleCount - 1;
        var bitIndex = 7;
        var byteIndex = 0;

        for (var col = this.moduleCount - 1; col > 0; col -= 2) {
            if (col == 6) col--;

            while (true) {
                for (var c = 0; c < 2; c++) {
                    if (this.modules[row][col - c] == null) {
                        var dark = false;

                        if (byteIndex < data.length) {
                            dark = (((data[byteIndex] >>> bitIndex) & 1) == 1);
                        }

                        var mask = QRCode.QRUtil.getMask(maskPattern, row, col - c);

                        if (mask) {
                            dark = !dark;
                        }

                        this.modules[row][col - c] = dark;
                        bitIndex--;

                        if (bitIndex == -1) {
                            byteIndex++;
                            bitIndex = 7;
                        }
                    }
                }

                row += inc;

                if (row < 0 || this.moduleCount <= row) {
                    row -= inc;
                    inc = -inc;
                    break;
                }
            }
        }
    }
};

// QR8bitByte 数据类
var QRCode8bitByte = function(data) {
    this.mode = QRCode.QRMode.MODE_8BIT_BYTE;
    this.data = data;
};

QRCode8bitByte.prototype = {
    getLength: function(buffer) {
        return this.data.length;
    },

    write: function(buffer) {
        for (var i = 0; i < this.data.length; i++) {
            buffer.put(this.data.charCodeAt(i), 8);
        }
    }
};

// QR模式常量
QRCode.QRMode = {
    MODE_NUMBER: 1 << 0,
    MODE_ALPHA_NUM: 1 << 1,
    MODE_8BIT_BYTE: 1 << 2,
    MODE_KANJI: 1 << 3
};

// 错误纠正级别
QRCode.QRErrorCorrectLevel = {
    L: 1,
    M: 0,
    Q: 3,
    H: 2
};

// QR工具类
QRCode.QRUtil = {
    PATTERN_POSITION_TABLE: [
        [],
        [6, 18],
        [6, 22],
        [6, 26],
        [6, 30],
        [6, 34],
        [6, 22, 38],
        [6, 24, 42],
        [6, 26, 46],
        [6, 28, 50],
        [6, 30, 54],
        [6, 32, 58],
        [6, 34, 62],
        [6, 26, 46, 66],
        [6, 26, 48, 70],
        [6, 26, 50, 74],
        [6, 30, 54, 78],
        [6, 30, 56, 82],
        [6, 30, 58, 86],
        [6, 34, 62, 90],
        [6, 28, 50, 72, 94],
        [6, 26, 50, 74, 98],
        [6, 30, 54, 78, 102],
        [6, 28, 54, 80, 106],
        [6, 32, 58, 84, 110],
        [6, 30, 58, 86, 114],
        [6, 34, 62, 90, 118],
        [6, 26, 50, 74, 98, 122],
        [6, 30, 54, 78, 102, 126],
        [6, 26, 52, 78, 104, 130],
        [6, 30, 56, 82, 108, 134],
        [6, 34, 60, 86, 112, 138],
        [6, 30, 58, 86, 114, 142],
        [6, 34, 62, 90, 118, 146],
        [6, 30, 54, 78, 102, 126, 150],
        [6, 24, 50, 76, 102, 128, 154],
        [6, 28, 54, 80, 106, 132, 158],
        [6, 32, 58, 84, 110, 136, 162],
        [6, 26, 54, 82, 110, 138, 166],
        [6, 30, 58, 86, 114, 142, 170]
    ],

    G15: (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0),
    G18: (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0),
    G15_MASK: (1 << 14) | (1 << 12) | (1 << 10) | (1 << 4) | (1 << 1),

    getBCHTypeInfo: function(data) {
        var d = data << 10;
        while (QRCode.QRUtil.getBCHDigit(d) - QRCode.QRUtil.getBCHDigit(QRCode.QRUtil.G15) >= 0) {
            d ^= (QRCode.QRUtil.G15 << (QRCode.QRUtil.getBCHDigit(d) - QRCode.QRUtil.getBCHDigit(QRCode.QRUtil.G15)));
        }
        return ((data << 10) | d) ^ QRCode.QRUtil.G15_MASK;
    },

    getBCHTypeNumber: function(data) {
        var d = data << 12;
        while (QRCode.QRUtil.getBCHDigit(d) - QRCode.QRUtil.getBCHDigit(QRCode.QRUtil.G18) >= 0) {
            d ^= (QRCode.QRUtil.G18 << (QRCode.QRUtil.getBCHDigit(d) - QRCode.QRUtil.getBCHDigit(QRCode.QRUtil.G18)));
        }
        return (data << 12) | d;
    },

    getBCHDigit: function(data) {
        var digit = 0;
        while (data != 0) {
            digit++;
            data >>>= 1;
        }
        return digit;
    },

    getPatternPosition: function(typeNumber) {
        return QRCode.QRUtil.PATTERN_POSITION_TABLE[typeNumber - 1];
    },

    getMask: function(maskPattern, i, j) {
        switch (maskPattern) {
            case 0: return (i + j) % 2 == 0;
            case 1: return i % 2 == 0;
            case 2: return j % 3 == 0;
            case 3: return (i + j) % 3 == 0;
            case 4: return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 == 0;
            case 5: return (i * j) % 2 + (i * j) % 3 == 0;
            case 6: return ((i * j) % 2 + (i * j) % 3) % 2 == 0;
            case 7: return ((i * j) % 3 + (i + j) % 2) % 2 == 0;
            default: throw new Error("bad maskPattern:" + maskPattern);
        }
    },

    getLostPoint: function(qrCode) {
        var moduleCount = qrCode.getModuleCount();
        var lostPoint = 0;

        // LEVEL1
        for (var row = 0; row < moduleCount; row++) {
            for (var col = 0; col < moduleCount; col++) {
                var sameCount = 0;
                var dark = qrCode.isDark(row, col);

                for (var r = -1; r <= 1; r++) {
                    if (row + r < 0 || moduleCount <= row + r) {
                        continue;
                    }

                    for (var c = -1; c <= 1; c++) {
                        if (col + c < 0 || moduleCount <= col + c) {
                            continue;
                        }

                        if (r == 0 && c == 0) {
                            continue;
                        }

                        if (dark == qrCode.isDark(row + r, col + c)) {
                            sameCount++;
                        }
                    }
                }

                if (sameCount > 5) {
                    lostPoint += (3 + sameCount - 5);
                }
            }
        }

        // LEVEL2
        for (var row = 0; row < moduleCount - 1; row++) {
            for (var col = 0; col < moduleCount - 1; col++) {
                var count = 0;
                if (qrCode.isDark(row, col)) count++;
                if (qrCode.isDark(row + 1, col)) count++;
                if (qrCode.isDark(row, col + 1)) count++;
                if (qrCode.isDark(row + 1, col + 1)) count++;
                if (count == 0 || count == 4) {
                    lostPoint += 3;
                }
            }
        }

        return lostPoint;
    }
};

// 数据创建
QRCode.createData = function(typeNumber, errorCorrectLevel, dataList) {
    var rsBlocks = QRCode.QRRSBlock.getRSBlocks(typeNumber, errorCorrectLevel);
    var buffer = new QRCode.QRBitBuffer();

    for (var i = 0; i < dataList.length; i++) {
        var data = dataList[i];
        buffer.put(data.mode, 4);
        buffer.put(data.getLength(), QRCode.QRUtil.getLengthInBits(data.mode, typeNumber));
        data.write(buffer);
    }

    var totalDataCount = 0;
    for (var i = 0; i < rsBlocks.length; i++) {
        totalDataCount += rsBlocks[i].dataCount;
    }

    if (buffer.getLengthInBits() > totalDataCount * 8) {
        throw new Error("code length overflow. (" + buffer.getLengthInBits() + ">" + totalDataCount * 8 + ")");
    }

    if (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {
        buffer.put(0, 4);
    }

    while (buffer.getLengthInBits() % 8 != 0) {
        buffer.putBit(false);
    }

    while (true) {
        if (buffer.getLengthInBits() >= totalDataCount * 8) {
            break;
        }
        buffer.put(0xEC, 8);

        if (buffer.getLengthInBits() >= totalDataCount * 8) {
            break;
        }
        buffer.put(0x11, 8);
    }

    return QRCode.createBytes(buffer, rsBlocks);
};

QRCode.createBytes = function(buffer, rsBlocks) {
    var offset = 0;
    var maxDcCount = 0;
    var maxEcCount = 0;
    var dcdata = new Array(rsBlocks.length);
    var ecdata = new Array(rsBlocks.length);

    for (var r = 0; r < rsBlocks.length; r++) {
        var dcCount = rsBlocks[r].dataCount;
        var ecCount = rsBlocks[r].totalCount - dcCount;

        maxDcCount = Math.max(maxDcCount, dcCount);
        maxEcCount = Math.max(maxEcCount, ecCount);

        dcdata[r] = new Array(dcCount);

        for (var i = 0; i < dcdata[r].length; i++) {
            dcdata[r][i] = 0xff & buffer.buffer[i + offset];
        }
        offset += dcCount;

        var rsPoly = QRCode.QRUtil.getErrorCorrectPolynomial(ecCount);
        var rawPoly = new QRCode.QRPolynomial(dcdata[r], rsPoly.getLength() - 1);

        var modPoly = rawPoly.mod(rsPoly);
        ecdata[r] = new Array(rsPoly.getLength() - 1);
        for (var i = 0; i < ecdata[r].length; i++) {
            var modIndex = i + modPoly.getLength() - ecdata[r].length;
            ecdata[r][i] = (modIndex >= 0) ? modPoly.get(modIndex) : 0;
        }
    }

    var totalCodeCount = 0;
    for (var i = 0; i < rsBlocks.length; i++) {
        totalCodeCount += rsBlocks[i].totalCount;
    }

    var data = new Array(totalCodeCount);
    var index = 0;

    for (var i = 0; i < maxDcCount; i++) {
        for (var r = 0; r < rsBlocks.length; r++) {
            if (i < dcdata[r].length) {
                data[index++] = dcdata[r][i];
            }
        }
    }

    for (var i = 0; i < maxEcCount; i++) {
        for (var r = 0; r < rsBlocks.length; r++) {
            if (i < ecdata[r].length) {
                data[index++] = ecdata[r][i];
            }
        }
    }

    return data;
};

// 位缓冲区
QRCode.QRBitBuffer = function() {
    this.buffer = new Array();
    this.length = 0;
};

QRCode.QRBitBuffer.prototype = {
    get: function(index) {
        var bufIndex = Math.floor(index / 8);
        return ((this.buffer[bufIndex] >>> (7 - index % 8)) & 1) == 1;
    },

    put: function(num, length) {
        for (var i = 0; i < length; i++) {
            this.putBit(((num >>> (length - i - 1)) & 1) == 1);
        }
    },

    getLengthInBits: function() {
        return this.length;
    },

    putBit: function(bit) {
        var bufIndex = Math.floor(this.length / 8);
        if (this.buffer.length <= bufIndex) {
            this.buffer.push(0);
        }

        if (bit) {
            this.buffer[bufIndex] |= (0x80 >>> (this.length % 8));
        }

        this.length++;
    }
};

// 简化的辅助函数
QRCode.QRUtil.getLengthInBits = function(mode, type) {
    if (1 <= type && type < 10) {
        switch(mode) {
            case QRCode.QRMode.MODE_NUMBER: return 10;
            case QRCode.QRMode.MODE_ALPHA_NUM: return 9;
            case QRCode.QRMode.MODE_8BIT_BYTE: return 8;
            case QRCode.QRMode.MODE_KANJI: return 8;
            default: throw new Error("mode:" + mode);
        }
    } else if (type < 27) {
        switch(mode) {
            case QRCode.QRMode.MODE_NUMBER: return 12;
            case QRCode.QRMode.MODE_ALPHA_NUM: return 11;
            case QRCode.QRMode.MODE_8BIT_BYTE: return 16;
            case QRCode.QRMode.MODE_KANJI: return 10;
            default: throw new Error("mode:" + mode);
        }
    } else if (type < 41) {
        switch(mode) {
            case QRCode.QRMode.MODE_NUMBER: return 14;
            case QRCode.QRMode.MODE_ALPHA_NUM: return 13;
            case QRCode.QRMode.MODE_8BIT_BYTE: return 16;
            case QRCode.QRMode.MODE_KANJI: return 12;
            default: throw new Error("mode:" + mode);
        }
    } else {
        throw new Error("type:" + type);
    }
};

// RS块
QRCode.QRRSBlock = function(totalCount, dataCount) {
    this.totalCount = totalCount;
    this.dataCount = dataCount;
};

QRCode.QRRSBlock.RS_BLOCK_TABLE = [
    [1, 26, 19],
    [1, 26, 16],
    [1, 26, 13],
    [1, 26, 9],
    [1, 44, 34],
    [1, 44, 28],
    [1, 44, 22],
    [1, 44, 16],
    [1, 70, 55],
    [2, 35, 17],
    [2, 35, 13],
    [4, 17, 9]
];

QRCode.QRRSBlock.getRSBlocks = function(typeNumber, errorCorrectLevel) {
    var rsBlock = QRCode.QRRSBlock.getRsBlockTable(typeNumber, errorCorrectLevel);
    if (rsBlock == undefined) {
        throw new Error("bad rs block @ typeNumber:" + typeNumber + "/errorCorrectLevel:" + errorCorrectLevel);
    }

    var length = rsBlock.length / 3;
    var list = new Array();

    for (var i = 0; i < length; i++) {
        var count = rsBlock[i * 3 + 0];
        var totalCount = rsBlock[i * 3 + 1];
        var dataCount = rsBlock[i * 3 + 2];

        for (var j = 0; j < count; j++) {
            list.push(new QRCode.QRRSBlock(totalCount, dataCount));
        }
    }

    return list;
};

QRCode.QRRSBlock.getRsBlockTable = function(typeNumber, errorCorrectLevel) {
    switch(errorCorrectLevel) {
        case QRCode.QRErrorCorrectLevel.L:
            return QRCode.QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 0];
        case QRCode.QRErrorCorrectLevel.M:
            return QRCode.QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 1];
        case QRCode.QRErrorCorrectLevel.Q:
            return QRCode.QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 2];
        case QRCode.QRErrorCorrectLevel.H:
            return QRCode.QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 3];
        default:
            return undefined;
    }
};

// 多项式
QRCode.QRPolynomial = function(num, shift) {
    if (num.length == undefined) {
        throw new Error(num.length + "/" + shift);
    }

    var offset = 0;
    while (offset < num.length && num[offset] == 0) {
        offset++;
    }

    this.num = new Array(num.length - offset + shift);
    for (var i = 0; i < num.length - offset; i++) {
        this.num[i] = num[i + offset];
    }
};

QRCode.QRPolynomial.prototype = {
    get: function(index) {
        return this.num[index];
    },

    getLength: function() {
        return this.num.length;
    },

    multiply: function(e) {
        var num = new Array(this.getLength() + e.getLength() - 1);

        for (var i = 0; i < this.getLength(); i++) {
            for (var j = 0; j < e.getLength(); j++) {
                num[i + j] ^= QRCode.QRMath.gexp(QRCode.QRMath.glog(this.get(i)) + QRCode.QRMath.glog(e.get(j)));
            }
        }

        return new QRCode.QRPolynomial(num, 0);
    },

    mod: function(e) {
        if (this.getLength() - e.getLength() < 0) {
            return this;
        }

        var ratio = QRCode.QRMath.glog(this.get(0)) - QRCode.QRMath.glog(e.get(0));
        var num = new Array(this.getLength());

        for (var i = 0; i < this.getLength(); i++) {
            num[i] = this.get(i);
        }

        for (var i = 0; i < e.getLength(); i++) {
            num[i] ^= QRCode.QRMath.gexp(QRCode.QRMath.glog(e.get(i)) + ratio);
        }

        return new QRCode.QRPolynomial(num, 0).mod(e);
    }
};

// 数学函数
QRCode.QRMath = {
    glog: function(n) {
        if (n < 1) {
            throw new Error("glog(" + n + ")");
        }
        return QRCode.QRMath.LOG_TABLE[n];
    },

    gexp: function(n) {
        while (n < 0) {
            n += 255;
        }
        while (n >= 256) {
            n -= 255;
        }
        return QRCode.QRMath.EXP_TABLE[n];
    },

    EXP_TABLE: new Array(256),
    LOG_TABLE: new Array(256)
};

for (var i = 0; i < 8; i++) {
    QRCode.QRMath.EXP_TABLE[i] = 1 << i;
}
for (var i = 8; i < 256; i++) {
    QRCode.QRMath.EXP_TABLE[i] = QRCode.QRMath.EXP_TABLE[i - 4] ^ QRCode.QRMath.EXP_TABLE[i - 5] ^ QRCode.QRMath.EXP_TABLE[i - 6] ^ QRCode.QRMath.EXP_TABLE[i - 8];
}
for (var i = 0; i < 255; i++) {
    QRCode.QRMath.LOG_TABLE[QRCode.QRMath.EXP_TABLE[i]] = i;
}

// 错误纠正多项式
QRCode.QRUtil.getErrorCorrectPolynomial = function(errorCorrectLength) {
    var a = new QRCode.QRPolynomial([1], 0);
    for (var i = 0; i < errorCorrectLength; i++) {
        a = a.multiply(new QRCode.QRPolynomial([1, QRCode.QRMath.gexp(i)], 0));
    }
    return a;
};

// 简化的Canvas API
window.QRCode = {
    toCanvas: function(canvas, text, options, callback) {
        try {
            // 处理参数
            if (typeof options === 'function') {
                callback = options;
                options = {};
            }

            // 如果canvas是字符串ID，获取元素
            if (typeof canvas === 'string') {
                canvas = document.getElementById(canvas);
            }

            // 创建QR码
            var qr = new QRCode(1, QRCode.QRErrorCorrectLevel.L);
            qr.addData(text);
            qr.make();

            // 渲染到Canvas
            var ctx = canvas.getContext('2d');
            var moduleCount = qr.getModuleCount();
            var moduleSize = Math.floor((options.width || 280) / moduleCount);
            var canvasSize = moduleSize * moduleCount;

            canvas.width = canvasSize;
            canvas.height = canvasSize;

            // 白色背景
            ctx.fillStyle = options.color?.light || '#FFFFFF';
            ctx.fillRect(0, 0, canvasSize, canvasSize);

            // 黑色模块
            ctx.fillStyle = options.color?.dark || '#000000';
            for (var row = 0; row < moduleCount; row++) {
                for (var col = 0; col < moduleCount; col++) {
                    if (qr.isDark(row, col)) {
                        ctx.fillRect(col * moduleSize, row * moduleSize, moduleSize, moduleSize);
                    }
                }
            }

            // 调用回调
            if (callback) {
                setTimeout(function() { callback(null); }, 10);
            }

        } catch (error) {
            console.error('QRCode生成错误:', error);
            if (callback) {
                callback(error);
            } else {
                throw error;
            }
        }
    }
};